# Règles pour l'agent Augment

## Structure du projet
- Le projet est divisé en deux parties principales : `api` (backend FastAPI) et `frontend` (React + TypeScript + Vite)
- Respecter cette séparation lors des suggestions de code
- Utiliser Docker pour lancer différentes commandes (pytest, ls, ...)

## Backend (API)
- Utiliser Python 3.10+ avec FastAPI
- Respecter les conventions de formatage avec ruff et mypy (strict mode)
- Utiliser SQLModel pour les modèles de données
- Utiliser Alembic pour les migrations de base de données
- Respecter les énumérations définies dans `constants/enumerations.py`
- Suivre le pattern de validation des affranchissements avec les classes ValidateurTypeAffranchissement

## Frontend
- Utiliser React avec TypeScript
- Respecter les règles ESLint configurées
- Utiliser les composants existants pour la cohérence
- Intégrer correctement avec l'API Scandit pour la lecture de codes-barres

## Domaine métier
- Le projet concerne la gestion des enveloppes et affranchissements postaux
- Respecter les règles métier pour les différents types d'affranchissements (SD, S10, TNUM, etc.)
- Comprendre le workflow des enveloppes (EDITION → VALORISATION → TERMINEE)
- Tenir compte des validations spécifiques pour chaque type d'affranchissement

## CI/CD
- Respecter la configuration GitLab CI pour les builds et déploiements
- Les modifications dans `/api` ou `/frontend` déclenchent des builds spécifiques

## Conventions de code
- Utiliser des noms de variables et fonctions en français
- Documenter les fonctions et classes importantes
- Suivre les patterns existants pour les nouveaux développements
- Ecris du code lisible et propre, applique les principes DRY / CLEAN CODE

## Commandes
- Utiliser docker compose api COMMANDE pour executer une commande sur l'API
- Utiliser docker compose frontend COMMANDE pour executer une commande sur le frontend.
Lire le `docker-compose.yml` pour connaitre les commandes disponibles et les chemins d'accès aux fichiers.
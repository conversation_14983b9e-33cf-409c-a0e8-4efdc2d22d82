from sqlalchemy import create_engine
from sqlalchemy_utils import create_database, database_exists, drop_database
from core.config import settings
from alembic.config import Config
from alembic import command
import glob
import os
from models import User, UserCreate, Site, Expediteur, Enveloppe, User<PERSON><PERSON>, <PERSON><PERSON>er, RegleMetier
from constants.enumerations import TypeRegleMetierEnum, StatutEnveloppeEnum, StatutCasierEnum, DestinationEnveloppeEnum
from datetime import datetime
from sqlmodel import select
from core.db import get_session
import crud
import subprocess
import glob

if os.getenv("ENVIRONMENT") != "local":
    print("N'applique pas les fixtures car ce n'est pas l'env de DEV")
    exit()

if os.getenv("POSTGRES_SERVER") != "db":
    print("N'applique pas les fixtures car la db n'est pas locale")
    exit()

# Fixtures DATA

with get_session() as session:

    # Active toutes les règles métiers séquences
    regles_sequences = session.exec(select(RegleMetier).where(RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE)).all()
    for regle in regles_sequences:
        regle.active = True
        session.add(regle)
    session.commit()
 
    dump_files = glob.glob('/app/tests/fixtures/dumps/*.dump')
    for dump_file in dump_files:
        db_uri = str(settings.SQLALCHEMY_DATABASE_URI).replace("postgresql+psycopg://", "postgresql://")
        try:
            subprocess.run(f"pg_restore -d {db_uri} {dump_file}".split(" "), check=True)
        except:
            pass

    # Create DATA - Vérifier d'abord si l'utilisateur existe
    existing_user = session.exec(select(User).where(User.email == settings.FIRST_SUPERUSER)).first()
    if not existing_user:
        user_in = UserCreate(
                email=settings.FIRST_SUPERUSER,
                password=settings.FIRST_SUPERUSER_PASSWORD,
                role=UserRole.ADMIN,
                site_id=session.exec(select(Site)).first().id
            )
        user = crud.create_user(session=session, user_create=user_in)
    
    existing_user = session.exec(select(User).where(User.email == settings.FIRST_SUPERUSER)).first()
    if existing_user:
        existing_user.role = UserRole.ADMIN
        existing_user.site_id = session.exec(select(Site)).first().id
        session.commit()
    
    # Ajouter un faux expéditeur (fraudeur) s'il n'existe pas
    fraudeur = session.query(Expediteur).filter(Expediteur.nom == "Fraudeur Test").first()
    if not fraudeur:
        fraudeur = Expediteur(nom="Fraudeur Test", adresse="123 Rue de la Fraude", siret="12345678901234")
        session.add(fraudeur)
        session.commit()


    # Ajouter des enveloppes frauduleuses pour cet expéditeur si nécessaire
    site = session.exec(select(Site)).first()
    enveloppes_fraudeur = len(session.exec(select(Enveloppe).where(Enveloppe.expediteur == fraudeur)).all())
    if enveloppes_fraudeur < 10:
        for i in range(10 - enveloppes_fraudeur):
            enveloppe = Enveloppe(
                destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
                statut=StatutEnveloppeEnum.TERMINEE,
                poids=100.0,
                surpoids=False,
                surdimensionne=False,
                expediteur=fraudeur,
                site_id=existing_user.site_id,
                user=existing_user,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(enveloppe)
        session.commit()

    # Créer 5 casier si pas existant
    casiers_existants = len(session.exec(select(Casier)).all())
    if casiers_existants < 5:
        for i in range(5 - casiers_existants):
            casier = Casier(
                site=site,
                numero=f"CASIER_{i+1}",
                emplacement=f"Emplacement {i+1}",
                capacite_max=50
            )
            session.add(casier)
        session.commit()

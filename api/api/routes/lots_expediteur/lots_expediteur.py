from fastapi import APIRouter, HTTPException, Query
from sqlmodel import Session, select
from typing import  Dict, Any
from api.deps import SessionDep, CurrentManagerUser
from models.business import Enveloppe, LotExpediteur
from constants.enumerations import StatutLotExpediteurEnum, OptionTraitementLotExpediteurEnum
from math import ceil
from models.public import PaginatedResponse, LotExpediteurPublic, LotExpediteurDetails, EnveloppeItemPublic, AffranchissementItemPublic, UserPublic
from services.lot_expediteur import ServiceLotExpediteur

router = APIRouter()

from fastapi import Response, Request
from sqlalchemy.orm import joinedload
from sqlalchemy import select as sql_select
import csv
from io import StringIO

@router.get("/", response_model=PaginatedResponse[LotExpediteurPublic])
def get_lots_expediteurs(
    *,
    request: Request,
    session: SessionDep,
    current_user: CurrentManagerUser,
    page: int = Query(1, ge=1, description="Numéro de la page"),
    page_size: int = Query(10, ge=1, le=100, description="Nombre d'éléments par page"),
    sort_by: str = Query("created_at", description="Champ de tri"),
    sort_order: str = Query("desc", description="Ordre de tri (asc ou desc)")
) -> PaginatedResponse[LotExpediteurPublic]:
    """
    Récupère l'ensemble des lots expéditeurs avec pagination
    """
    lots_query = sql_select(LotExpediteur).options(
        joinedload(LotExpediteur.enveloppes),
        joinedload(LotExpediteur.expediteur),
        joinedload(LotExpediteur.casier)
    )

    # Filters
    query_params = request.query_params
    filters = {key: value for key, value in query_params.items() if key not in {"page", "page_size", "sort_by", "sort_order"}}

    # Appliquer les filtres dynamiques
    if 'statut' in filters:
        lots_query = lots_query.where(LotExpediteur.statut == filters['statut'])

    if 'expediteur_id' in filters:
        lots_query = lots_query.where(LotExpediteur.expediteur_id == int(filters['expediteur_id']))

    if 'site_id' in filters:
        lots_query = lots_query.where(LotExpediteur.site_id == int(filters['site_id']))

    # Récupérer tous les lots pour le tri global
    all_lots = session.execute(lots_query).unique().scalars().all()
    
    # Convertir tous les lots en LotExpediteurPublic
    all_lots_public = [
        LotExpediteurPublic.model_validate(lot, update={"nombre_enveloppes": len(lot.enveloppes), "valorisation": ServiceLotExpediteur.calcul_somme_valorisation(lot)}) for lot in all_lots
    ]
    
    # Appliquer le tri sur l'ensemble des résultats
    if sort_by == "sous_affranchissement":
        all_lots_public.sort(key=lambda x: x.valorisation['postage']['montant_sous_affranchissement'], reverse=sort_order == "desc")
    else:
        all_lots_public.sort(key=lambda x: getattr(x, sort_by), reverse=sort_order == "desc")
    
    # Calculer la pagination
    total_items = len(all_lots_public)
    total_pages = ceil(total_items / page_size)
    
    # Appliquer la pagination après le tri
    lots_public = all_lots_public[(page - 1) * page_size:page * page_size]

    return PaginatedResponse(
        total_pages=total_pages,
        current_page=page,
        page_size=page_size,
        total_items=total_items,
        items=lots_public
    )


@router.get("/{lot_id}", response_model=LotExpediteurDetails)
def get_lot_expediteur_by_id(
    *,
    session: SessionDep,
    current_user: CurrentManagerUser,
    lot_id: int
) -> LotExpediteurDetails:
    """Récupère un lot expéditeur par son ID avec tous les détails des enveloppes"""

    lot = session.execute(sql_select(LotExpediteur).options(
        joinedload(LotExpediteur.enveloppes).joinedload(Enveloppe.affranchissements),
        joinedload(LotExpediteur.enveloppes).joinedload(Enveloppe.photos),
        joinedload(LotExpediteur.enveloppes).joinedload(Enveloppe.site),
        joinedload(LotExpediteur.enveloppes).joinedload(Enveloppe.expediteur),
        joinedload(LotExpediteur.enveloppes).joinedload(Enveloppe.destination),
        joinedload(LotExpediteur.enveloppes).joinedload(Enveloppe.casier),
        joinedload(LotExpediteur.enveloppes).joinedload(Enveloppe.user),
        joinedload(LotExpediteur.expediteur),
        joinedload(LotExpediteur.casier),
        joinedload(LotExpediteur.site)
    ).where(LotExpediteur.id == lot_id)).unique().scalar_one_or_none()
    
    if not lot:
        raise HTTPException(status_code=404, detail="Lot expéditeur non trouvé")
    
    # Convert envelopes to public format
    enveloppes_public = []
    for enveloppe in lot.enveloppes:
        # Convert affranchissements
        affranchissements_public = []
        for aff in enveloppe.affranchissements:
            aff_public = AffranchissementItemPublic.model_validate(aff, update={
                "informations": {},  # Add any computed fields
                "donnees": None
            })
            affranchissements_public.append(aff_public)
        
        # Convert user
        user_public = None
        if enveloppe.user:
            user_public = UserPublic.model_validate(enveloppe.user)
        
        # Create envelope public
        enveloppe_public = EnveloppeItemPublic.model_validate(enveloppe, update={
            "affranchissements": affranchissements_public,
            "user": user_public,
            "informations": {}  # Add any computed fields
        })
        enveloppes_public.append(enveloppe_public)
    
    # Calculate valorisation
    valorisation = ServiceLotExpediteur.calcul_somme_valorisation(lot)
    
    return LotExpediteurDetails.model_validate(lot, update={
        "nombre_enveloppes": len(lot.enveloppes),
        "valorisation": valorisation,
        "enveloppes": enveloppes_public
    })


@router.get("/{lot_id}/csv", response_class=Response)
def get_lot_expediteur_csv(
    *,
    session: SessionDep,
    current_user: CurrentManagerUser,
    lot_id: int
):
    """
    Génère un CSV contenant les codes d'affranchissement pour chaque enveloppe d'un lot_expediteur
    """
    lot = session.execute(sql_select(LotExpediteur).options(
        joinedload(LotExpediteur.enveloppes).joinedload(Enveloppe.affranchissements),
        joinedload(LotExpediteur.casier)
    ).where(LotExpediteur.id == lot_id)).unique().scalar_one_or_none()
    
    if not lot:
        raise HTTPException(status_code=404, detail="Lot expéditeur non trouvé")
    
    output = StringIO()
    writer = csv.writer(output)
    writer.writerow(["Site", "Casier", "Enveloppe ID", "Produit", "Expéditeur", "Codes d'affranchissement", "Valorisation Cas A", "Valorisation Cas C"])
    
    total_valorisation_cas_a = 0
    total_valorisation_cas_c = 0
    
    for enveloppe in lot.enveloppes:
        codes = [aff.code for aff in enveloppe.affranchissements if aff.code and aff.code != ""]
        valorisation_cas_a = enveloppe.valorisation.get('livraison', {}).get('cout_total', 0) if enveloppe.valorisation else 0
        valorisation_cas_c = enveloppe.valorisation.get('expédition', {}).get('cout_ttc', 0) if enveloppe.valorisation else 0
        
        total_valorisation_cas_a += valorisation_cas_a
        total_valorisation_cas_c += valorisation_cas_c
        
        writer.writerow([
            enveloppe.casier.site.nom if enveloppe.casier else "?",
            enveloppe.casier.numero if enveloppe.casier else "?",
            enveloppe.id,
            enveloppe.destination_enveloppe.value,
            enveloppe.expediteur.nom if enveloppe.expediteur else "INCONNU",
            ", ".join(codes),
            f"{valorisation_cas_a:.2f}",
            f"{valorisation_cas_c:.2f}"
        ])
    
    writer.writerow(["Total", "", "", "", "", f"{total_valorisation_cas_a:.2f}", f"{total_valorisation_cas_c:.2f}"])
    
    return Response(
        content=output.getvalue(),
        media_type="text/csv",
        headers={"Content-Disposition": f"attachment; filename=lot_expediteur_{lot_id}.csv"}
    )


@router.post("/{lot_id}/terminer", response_model=LotExpediteur)
def terminer_lot_expediteur(
    *,
    session: SessionDep,
    current_user: CurrentManagerUser,
    lot_id: int
):
    """
    Termine un lot expéditeur, change son statut et relâche le casier associé
    """
    lot = session.execute(sql_select(LotExpediteur).where(LotExpediteur.id == lot_id)).scalar_one_or_none()
    if not lot:
        raise HTTPException(status_code=404, detail="Lot expéditeur non trouvé")
    
    # Changer le statut du lot
    lot.statut = StatutLotExpediteurEnum.TRAITE_OPERATIONNEL
    
    # Relâcher le casier
    if lot.casier:
        lot.casier = None

    # Pour toutes les enveloppes du Lot
    for enveloppe in lot.enveloppes:
        # Changer le statut de l'enveloppe
        enveloppe.casier = None

    session.commit()
    session.refresh(lot)
    return lot

@router.patch("/{lot_id}/statut", response_model=LotExpediteur)
def update_statut_lot_expediteur(
    *,
    session: SessionDep,
    current_user: CurrentManagerUser,
    lot_id: int,
    statut: StatutLotExpediteurEnum
):
    """
    Met à jour uniquement le statut d'un lot expéditeur
    en respectant la hiérarchie des statuts
    """
    lot = session.execute(sql_select(LotExpediteur).where(LotExpediteur.id == lot_id)).scalar_one_or_none()

    if not lot:
        raise HTTPException(status_code=404, detail="Lot expéditeur non trouvé")
    
    # Vérifier si le changement de statut respecte la hiérarchie
    if not ServiceLotExpediteur(session).changer_statut_lot_avec_verification(lot, statut, current_user):
        raise HTTPException(status_code=400, detail="Changement de statut interdit")
       
    session.refresh(lot)
    return lot

@router.get("/{lot_id}/valorisation", response_model=Dict[str, Any])
def get_lot_expediteur_valorisation(
    *,
    session: SessionDep,
    current_user: CurrentManagerUser,
    lot_id: int
) -> Dict[str, Any]:
    """
    Calcule et retourne le cumul des valeurs de valorisation pour l'ensemble des enveloppes d'un lot
    """
    lot = session.execute(sql_select(LotExpediteur).options(
        joinedload(LotExpediteur.enveloppes)
    ).where(LotExpediteur.id == lot_id)).unique().scalar_one_or_none()
    
    if not lot:
        raise HTTPException(status_code=404, detail="Lot expéditeur non trouvé")
    
    return ServiceLotExpediteur.calcul_somme_valorisation(lot)

@router.post("/", response_model=LotExpediteurPublic)
def create_lot_expediteur(
    *,
    session: SessionDep,
    current_user: CurrentManagerUser,
    expediteur_id: int = Query(..., description="ID de l'expéditeur")
) -> LotExpediteurPublic:
    """
    Crée manuellement un nouveau lot expéditeur
    """    
    site = current_user.site
    if not site:
        raise HTTPException(status_code=404, detail="Site non trouvé")
    
    # Créer le lot manuellement
    # Vérifier si un lot avec ces paramètres existe déjà
    existing_lot = session.execute(sql_select(LotExpediteur).where(
        LotExpediteur.expediteur_id == expediteur_id,
        LotExpediteur.statut == StatutLotExpediteurEnum.OUVERT,
        LotExpediteur.site_id == site.id
    )).scalar_one_or_none()

    if existing_lot:
        raise HTTPException(status_code=400, detail="Un lot ouvert existe déjà pour cet expéditeur sur ce site")

    lot = LotExpediteur(
        expediteur_id=expediteur_id,
        statut=StatutLotExpediteurEnum.OUVERT,
        site_id=current_user.site.id
    )
    
    session.add(lot)
    session.commit()
    session.refresh(lot)

    # Obtenir un casier disponible pour le site
    ServiceLotExpediteur(session).attribuer_casier_lot(lot)
    
    session.commit()
    session.refresh(lot)
    
    # Convertir en modèle public
    return LotExpediteurPublic.model_validate(lot, update={"nombre_enveloppes": 0})

@router.post("/{lot_id}/nouveau-casier", response_model=LotExpediteur)
def attribuer_nouveau_casier(
    *,
    session: SessionDep,
    current_user: CurrentManagerUser,
    lot_id: int
):
    """
    Attribue un nouveau casier à un lot expéditeur existant.
    L'ancien casier reste associé aux enveloppes déjà traitées.
    """
    lot = session.execute(sql_select(LotExpediteur).where(LotExpediteur.id == lot_id)).scalar_one_or_none()
    if not lot:
        raise HTTPException(status_code=404, detail="Lot expéditeur non trouvé")
    
    if lot.statut != StatutLotExpediteurEnum.OUVERT:
        raise HTTPException(
            status_code=400, 
            detail="Impossible d'attribuer un nouveau casier à un lot qui n'est pas ouvert"
        )
    
    # Obtenir un nouveau casier disponible
    ServiceLotExpediteur(session).attribuer_casier_lot(lot)
    
    session.commit()
    session.refresh(lot)
    return lot

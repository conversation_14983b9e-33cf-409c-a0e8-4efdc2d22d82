from fastapi import APIRouter, Query, Request
from api.deps import SessionDep, CurrentUser
from models.business import <PERSON><PERSON><PERSON><PERSON>, Expediteur, Affranchissement
from sqlalchemy.orm import joinedload, contains_eager
from models.public import EnveloppeItemPublic
from math import ceil
from models.public import PaginatedResponse
from models.users import User<PERSON><PERSON>, User
from sqlalchemy import or_, func
from sqlmodel import select

router = APIRouter()

@router.get("/", response_model=PaginatedResponse[EnveloppeItemPublic])
def get_enveloppes(
    *,
    request: Request,
    session: SessionDep,
    current_user: CurrentUser,
    page: int = Query(1, ge=1, description="Numéro de la page"),
    page_size: int = Query(10, ge=1, le=100, description="Nombre d'éléments par page")
) -> PaginatedResponse[EnveloppeItemPublic]:
    """
    Récupère toutes les enveloppes avec des options de filtrage et de pagination.
    """
    # Optimisation: Utiliser une sous-requête pour le comptage et la pagination
    query_params = request.query_params
    params_to_exclude = {"skip", "limit", "sort_by", "sort_order", "search", "recherche", "page", "page_size"}
    filters = {key: value for key, value in query_params.items() if key not in params_to_exclude}
    search_term = query_params.get("recherche") or query_params.get("search")
    
    # Construire la requête de base avec les filtres
    base_query = select(Enveloppe.id)
    
    # Appliquer les filtres
    if current_user.role == UserRole.USER:
        base_query = base_query.where(Enveloppe.user_id == current_user.id)

    if 'user_email' in filters and current_user.role != UserRole.USER:
        base_query = base_query.join(User).where(User.email.like(f"%{filters['user_email']}%"))

    if 'statut' in filters:
        base_query = base_query.where(Enveloppe.statut == filters['statut'])

    if 'expediteur_nom' in filters:
        base_query = base_query.join(Enveloppe.expediteur).where(Expediteur.nom.ilike(f"%{filters['expediteur_nom']}%"))

    # Recherche globale
    if search_term:
        base_query = base_query.outerjoin(User).outerjoin(Enveloppe.expediteur).outerjoin(Enveloppe.affranchissements)
        base_query = base_query.where(
            or_(
                User.email.ilike(f"%{search_term}%"),
                Expediteur.nom.ilike(f"%{search_term}%"),
                Affranchissement.code.ilike(f"%{search_term}%")
            )
        )

    # Optimisation: Compter directement avec une sous-requête
    total_items = len(session.exec(base_query.distinct()).all())
    total_pages = ceil(total_items / page_size)

    # Optimisation: Récupérer les IDs en une seule requête
    ids_query = base_query.distinct().order_by(Enveloppe.id.desc())
    paginated_ids = session.exec(ids_query.offset((page - 1) * page_size).limit(page_size)).all()
    enveloppe_ids = list(paginated_ids)
    
    # Si aucun résultat, retourner une réponse vide
    if not enveloppe_ids:
        return PaginatedResponse(
            total_pages=total_pages,
            current_page=page,
            page_size=page_size,
            total_items=total_items,
            items=[]
        )
    
    # Optimisation: Charger les données complètes en une seule requête
    enveloppes = session.exec(select(Enveloppe).where(
        Enveloppe.id.in_(enveloppe_ids)
    ).options(
        joinedload(Enveloppe.affranchissements),
        joinedload(Enveloppe.photos),
        joinedload(Enveloppe.site),
        joinedload(Enveloppe.destination),
        joinedload(Enveloppe.expediteur)
    )).unique().all()
    
    # Trier les résultats selon l'ordre des IDs récupérés
    sorted_enveloppes = sorted(enveloppes, key=lambda e: enveloppe_ids.index(e.id))
    
    return PaginatedResponse(
        total_pages=total_pages,
        current_page=page,
        page_size=page_size,
        total_items=total_items,
        items=sorted_enveloppes
    )



import os
from elasticsearch import Elasticsearch
from elasticsearch.exceptions import ConnectionError
from tqdm import tqdm
from contextlib import contextmanager
from sqlalchemy.exc import SQLAlchemyError
from typing import Iterator, Dict, Any
from models.business import CodeAffranchissementFrauduleux
from sqlmodel import select
from core.db import get_session

ELASTIC_HOST = "https://elastic.fraude-server-sta.aks.sandbox.innovation-laposte.io:443"
BATCH_SIZE = 10000
SCROLL_TIME = "2m"
INDEX_NAME = "&myapp.xtra_coll_wissous_code"

def get_elasticsearch_client() -> Elasticsearch:
    user = "elastic"
    password = os.getenv("ELASTIC_PASSWORD")
    if not password:
        raise ValueError("ELASTIC_PASSWORD environment variable is not set")
    
    return Elasticsearch(
        ELASTIC_HOST,
        basic_auth=(user, password)
    )

def scroll_documents(es: Elasticsearch) -> Iterator[Dict[str, Any]]:
    try:
        result = es.search(
            index=INDEX_NAME,
            scroll=SCROLL_TIME,
            body={
                "query": {"match_all": {}},
                "size": BATCH_SIZE
            }
        )
        
        total_docs = result['hits']['total']['value']
        scroll_id = result['_scroll_id']
        
        try:
            while True:
                hits = result['hits']['hits']
                if not hits:
                    break
                    
                for hit in hits:
                    yield hit['_source'], total_docs

                result = es.scroll(scroll_id=scroll_id, scroll=SCROLL_TIME)
                scroll_id = result['_scroll_id']
                
        finally:
            es.clear_scroll(scroll_id=scroll_id)
            
    except ConnectionError as e:
        raise ConnectionError(f"Erreur de connexion à Elasticsearch: {str(e)}")

def import_codes():
    try:
        es = get_elasticsearch_client()
        
        with get_session() as session:
            # Récupérer les codes existants
            existing_codes = {
                code.code for code in
                session.exec(select(CodeAffranchissementFrauduleux)).all()
            }
            
            # Préparer les données pour l'insertion en masse
            documents_to_insert = []
            total_docs = None
            
            # Initialiser la barre de progression avec le nombre total de documents
            with tqdm(desc="Traitement des documents", total=None) as pbar:
                for document, total in scroll_documents(es):
                    if total_docs is None:
                        total_docs = total
                        pbar.total = total_docs
                    
                    # Vérifier si le code n'existe pas déjà avant de l'ajouter
                    code = document['id']
                    if code not in existing_codes:
                        documents_to_insert.append({
                            'code': code
                        })
                        # Ajouter le code à existing_codes pour éviter les doublons dans le même lot
                        existing_codes.add(code)
                    
                    if len(documents_to_insert) >= BATCH_SIZE:
                        session.bulk_insert_mappings(
                            CodeAffranchissementFrauduleux,
                            documents_to_insert
                        )
                        documents_to_insert = []
                        session.flush()
                    
                    pbar.update(1)
                
                # Insérer les documents restants
                if documents_to_insert:
                    session.bulk_insert_mappings(
                        CodeAffranchissementFrauduleux,
                        documents_to_insert
                    )
                
                session.commit()
                
    except Exception as e:
        print(f"Erreur lors du traitement: {str(e)}")
        raise

def import_wissou_code():
    with get_session() as session:
        count = len(session.exec(select(CodeAffranchissementFrauduleux)).all())
        print(f"Nombre de codes frauduleux dans la base de données : {count}")
    import_codes()



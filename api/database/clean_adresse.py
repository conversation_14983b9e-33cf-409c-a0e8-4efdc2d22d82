#!/usr/bin/env python
"""
Script pour nettoyer les adresses des expéditeurs en séparant le code postal et la ville.
"""
from sqlalchemy import func, text
from sqlmodel import select
from core.db import get_session
from models import Expediteur
import re
from typing import <PERSON>ple, Optional

def extraire_code_postal_ville(adresse: str) -> Tuple[str, str, str]:
    """
    Extrait le code postal et la ville d'une adresse.
    Retourne (adresse_nettoyée, code_postal, ville)
    """
    if not adresse:
        return "", "", ""
    
    # Motif pour trouver un code postal français (5 chiffres) suivi d'une ville
    pattern = r'(.*?)[\s,]+(\d{5})[\s,]+([^,]+)(?:\.)?$'
    match = re.search(pattern, adresse)
    
    if match:
        adresse_clean = match.group(1).strip()
        code_postal = match.group(2).strip()
        ville = match.group(3).strip()
        return adresse_clean, code_postal, ville
    
    return adresse, "", ""

def nettoyer_adresses_expediteurs():
    """
    Parcourt tous les expéditeurs et nettoie leurs adresses
    en séparant le code postal et la ville.
    """
    session = get_session()
    
    # Récupérer tous les expéditeurs avec une adresse non vide
    expediteurs = session.exec(select(Expediteur).where(
        Expediteur.adresse.isnot(None),
        func.length(Expediteur.adresse) > 0
    )).all()
    
    print(f"Traitement de {len(expediteurs)} expéditeurs...")
    
    modifies = 0
    for expediteur in expediteurs:
        # Si l'expéditeur a déjà un code postal et une ville, on le saute
        if expediteur.code_postal and expediteur.ville:
            continue
            
        adresse_clean, code_postal, ville = extraire_code_postal_ville(expediteur.adresse)
        
        if code_postal and ville:
            expediteur.adresse = adresse_clean
            expediteur.code_postal = code_postal
            expediteur.ville = ville
            modifies += 1
            
            if modifies % 100 == 0:
                print(f"Traités: {modifies}/{len(expediteurs)}")
    
    print(f"Mise à jour de {modifies} adresses d'expéditeurs")
    
    if modifies > 0:
        confirmation = input(f"Confirmer la mise à jour de {modifies} adresses ? (y/n): ")
        if confirmation.lower() == 'y':
            session.commit()
            print("Modifications enregistrées avec succès.")
        else:
            session.rollback()
            print("Opération annulée.")
    else:
        print("Aucune modification à effectuer.")

if __name__ == "__main__":
    nettoyer_adresses_expediteurs()
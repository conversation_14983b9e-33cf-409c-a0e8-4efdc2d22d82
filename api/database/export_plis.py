from sqlmodel import Session, select
from datetime import datetime
import csv
from models.business import Enveloppe, Expediteur, Destination, Site, Casier
from sqlalchemy import and_
from sqlalchemy.orm import joinedload

def export_enveloppes_to_csv(date_debut: datetime, date_fin: datetime, output_file: str, session: Session):
    """
    Exporte les enveloppes dans un fichier CSV pour une période donnée.
    
    Args:
        date_debut (datetime): Date de début de la période
        date_fin (datetime): Date de fin de la période
        output_file (str): Chemin du fichier CSV de sortie
        session (Session): Session SQLModel
    """
    # Requête pour récupérer les enveloppes avec leurs relations
    enveloppes = session.execute(select(Enveloppe).options(
        joinedload(Enveloppe.expediteur),
        joinedload(Enveloppe.destination),
        joinedload(Enveloppe.user)
    ).where(
        Enveloppe.created_at >= date_debut,
        Enveloppe.created_at <= date_fin
    ).order_by(Enveloppe.created_at.desc())).unique().scalars().all()
    
    # Définition des en-têtes du CSV
    headers = [
        'ID', 'User', 'Site', 'Expediteur', 'Date création'
    ]
    
    # Écriture du fichier CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=headers)
        writer.writeheader()
        
        for enveloppe in enveloppes:
            row = {
                'ID': enveloppe.id,
                'User': enveloppe.user.email if enveloppe.user else '',
                'Site': enveloppe.site.nom if enveloppe.site else '',
                'Expediteur': enveloppe.expediteur.nom if enveloppe.expediteur else '',
                'Date création': enveloppe.created_at.strftime('%Y-%m-%d %H:%M:%S') if enveloppe.created_at else ''
            }
            writer.writerow(row)

# Exemple d'utilisation
if __name__ == "__main__":
    from core.db import get_session
    
    # Création de la session
    with get_session() as session:
        # Définition de la période
        date_debut = datetime(2025, 5, 19)
        date_fin = datetime(2025, 6, 30)
        
        # Export des données
        export_enveloppes_to_csv(
            date_debut=date_debut,
            date_fin=date_fin,
            output_file="enveloppes_export.csv",
            session=session
        )
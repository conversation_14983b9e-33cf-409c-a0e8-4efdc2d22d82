#!/usr/bin/env python
"""
Script pour identifier et fusionner les expéditeurs en doublon.
Demande une confirmation pour chaque fusion.
"""
from sqlalchemy import func, text
from sqlmodel import select
from core.db import get_session
from models import Expediteur, Enveloppe, LotExpediteur, MessageExpediteur
import difflib
from typing import List, Dict, Tuple, Set
import sys

def normaliser_texte(texte: str) -> str:
    """Normalise un texte pour la comparaison (minuscules, sans espaces superflus)"""
    if not texte:
        return ""
    return " ".join(texte.lower().strip().split())

def calculer_similarite(str1: str, str2: str) -> float:
    """Calcule la similarité entre deux chaînes"""
    if not str1 or not str2:
        return 0.0
    return difflib.SequenceMatcher(None, normaliser_texte(str1), normaliser_texte(str2)).ratio()

def trouver_expediteurs_similaires(session) -> List[List[Expediteur]]:
    """Trouve les groupes d'expéditeurs similaires"""
    tous_expediteurs = session.exec(select(Expediteur)).all()
    
    # Dictionnaire pour regrouper les expéditeurs par nom normalisé
    expediteurs_par_nom: Dict[str, List[Expediteur]] = {}
    
    # Premier regroupement par nom exact normalisé
    for exp in tous_expediteurs:
        nom_normalise = normaliser_texte(exp.nom)
        if nom_normalise not in expediteurs_par_nom:
            expediteurs_par_nom[nom_normalise] = []
        expediteurs_par_nom[nom_normalise].append(exp)
    
    # Filtrer pour ne garder que les groupes avec plus d'un expéditeur
    groupes_similaires = [groupe for groupe in expediteurs_par_nom.values() if len(groupe) > 1]
    
    # Ajouter les groupes avec similarité élevée mais pas exactement le même nom
    noms_deja_traites: Set[int] = set()
    for i, exp1 in enumerate(tous_expediteurs):
        if exp1.id in noms_deja_traites:
            continue
            
        groupe_similaire = [exp1]
        for j, exp2 in enumerate(tous_expediteurs[i+1:], i+1):
            if exp2.id in noms_deja_traites:
                continue
                
            # Vérifier la similarité des noms
            similarite = calculer_similarite(exp1.nom, exp2.nom)
            
            # Si les noms sont très similaires (mais pas identiques après normalisation)
            if similarite > 0.85 and normaliser_texte(exp1.nom) != normaliser_texte(exp2.nom):
                # Vérifier aussi l'adresse pour plus de certitude
                similarite_adresse = calculer_similarite(exp1.adresse or "", exp2.adresse or "")
                if similarite_adresse > 0.7:  # Si l'adresse est aussi similaire
                    groupe_similaire.append(exp2)
                    noms_deja_traites.add(exp2.id)
        
        if len(groupe_similaire) > 1:
            groupes_similaires.append(groupe_similaire)
            noms_deja_traites.add(exp1.id)
    
    return groupes_similaires

def fusionner_expediteurs(session, expediteur_principal: Expediteur, expediteurs_a_fusionner: List[Expediteur]) -> None:
    """Fusionne plusieurs expéditeurs dans l'expéditeur principal"""
    
    # Récupérer les IDs des expéditeurs à fusionner
    ids_a_fusionner = [exp.id for exp in expediteurs_a_fusionner]
    
    # Mettre à jour les enveloppes
    session.execute(
        text("UPDATE enveloppe SET expediteur_id = :principal_id WHERE expediteur_id = ANY(:ids_a_fusionner)"),
        {"principal_id": expediteur_principal.id, "ids_a_fusionner": ids_a_fusionner}
    )
    
    # Mettre à jour les lots d'expéditeurs
    session.execute(
        text("UPDATE lotexpediteur SET expediteur_id = :principal_id WHERE expediteur_id = ANY(:ids_a_fusionner)"),
        {"principal_id": expediteur_principal.id, "ids_a_fusionner": ids_a_fusionner}
    )
    
    # Mettre à jour les messages d'expéditeurs
    session.execute(
        text("UPDATE messageexpediteur SET expediteur_id = :principal_id WHERE expediteur_id = ANY(:ids_a_fusionner)"),
        {"principal_id": expediteur_principal.id, "ids_a_fusionner": ids_a_fusionner}
    )
    
    # Supprimer les expéditeurs fusionnés
    for expediteur in expediteurs_a_fusionner:
        session.delete(expediteur)

def afficher_details_expediteur(expediteur: Expediteur, session) -> None:
    """Affiche les détails d'un expéditeur avec le nombre d'enveloppes associées"""
    nb_enveloppes = len(session.exec(select(Enveloppe).where(Enveloppe.expediteur_id == expediteur.id)).all())
    nb_lots = len(session.exec(select(LotExpediteur).where(LotExpediteur.expediteur_id == expediteur.id)).all())
    
    print(f"ID: {expediteur.id}")
    print(f"ID: {expediteur.id_migration}")
    print(f"Nom: {expediteur.nom}")
    print(f"Adresse: {expediteur.adresse}")
    print(f"Ville: {expediteur.ville}")
    print(f"Code postal: {expediteur.code_postal}")
    print(f"Pays: {expediteur.pays}")
    print(f"SIRET: {expediteur.siret}")
    print(f"Nombre d'enveloppes: {nb_enveloppes}")
    print(f"Nombre de lots: {nb_lots}")
    print("-" * 40)

def compter_champs_remplis(expediteur: Expediteur) -> int:
    """Compte le nombre de champs non vides pour un expéditeur"""
    champs = [
        expediteur.nom, 
        expediteur.adresse, 
        expediteur.ville, 
        expediteur.code_postal, 
        expediteur.pays, 
        expediteur.siret
    ]
    return sum(1 for champ in champs if champ and champ.strip())

def choisir_meilleur_expediteur(session, groupe: List[Expediteur]) -> Tuple[Expediteur, int]:
    """Choisit le meilleur expéditeur en fonction du nombre d'enveloppes et des champs remplis"""
    # Créer une liste de tuples (expediteur, nb_enveloppes, nb_champs_remplis)
    expediteurs_info = []
    for exp in groupe:
        nb_enveloppes = len(session.exec(select(Enveloppe).where(Enveloppe.expediteur_id == exp.id)).all())
        nb_champs_remplis = compter_champs_remplis(exp)
        expediteurs_info.append((exp, nb_enveloppes, nb_champs_remplis))
    
    # Trier d'abord par nombre d'enveloppes, puis par nombre de champs remplis
    expediteurs_info.sort(key=lambda x: (x[1], x[2]), reverse=True)
    
    # Retourner le meilleur expéditeur et son index dans le groupe original
    meilleur_expediteur = expediteurs_info[0][0]
    index = groupe.index(meilleur_expediteur) + 1
    
    return meilleur_expediteur, index

def main():
    with get_session() as session:
        # Trouver les groupes d'expéditeurs similaires
        groupes_similaires = trouver_expediteurs_similaires(session)
        
        if not groupes_similaires:
            print("Aucun expéditeur en doublon trouvé.")
            return
        
        print(f"Nombre de groupes d'expéditeurs similaires trouvés: {len(groupes_similaires)}")
        
        for i, groupe in enumerate(groupes_similaires, 1):
            print(f"\nGroupe {i} ({len(groupe)} expéditeurs similaires):")
            print("=" * 50)
            
            # Afficher les détails de chaque expéditeur du groupe
            for j, exp in enumerate(groupe, 1):
                print(f"Expéditeur {j}:")
                afficher_details_expediteur(exp, session)
            
            # Demander confirmation pour la fusion
            choix = input(f"Fusionner ce groupe ? (o/n/q pour quitter): ").lower()
            if choix == 'q':
                print("Opération annulée.")
                return
            
            if choix == 'o':
                # Choisir le meilleur expéditeur (plus d'enveloppes et plus de champs remplis)
                expediteur_suggere, index_suggestion = choisir_meilleur_expediteur(session, groupe)
                
                print(f"Suggestion: Expéditeur {index_suggestion} (priorité: nombre d'enveloppes puis complétude des informations)")
                
                # Demander quel expéditeur conserver comme principal
                while True:
                    try:
                        choix_principal = input(f"Quel expéditeur conserver comme principal (1-{len(groupe)}, Entrée pour {index_suggestion}) ? ")
                        if choix_principal == "":
                            choix_principal = index_suggestion
                        else:
                            choix_principal = int(choix_principal)
                            
                        if 1 <= choix_principal <= len(groupe):
                            break
                        print(f"Veuillez entrer un nombre entre 1 et {len(groupe)}.")
                    except ValueError:
                        print("Veuillez entrer un nombre valide.")
                
                expediteur_principal = groupe[choix_principal - 1]
                expediteurs_a_fusionner = [exp for i, exp in enumerate(groupe) if i != choix_principal - 1]
                
                print(f"Fusion en cours... Expéditeur principal: {expediteur_principal.nom} (ID: {expediteur_principal.id})")
                fusionner_expediteurs(session, expediteur_principal, expediteurs_a_fusionner)
                
                session.commit()
            else:
                print("Groupe ignoré.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOpération annulée par l'utilisateur.")
        sys.exit(1)

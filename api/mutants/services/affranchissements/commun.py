from constants.affranchissements.nature import NATURES_AFFRANCHISSEMENT
from constants.enumerations import NatureAffranchissementEnum, TypeAffranchissementEnum, DeviseEnum, ValiditeAffranchissementEnum
from models.business import Affranchissement
from core.utils.prix import convertir_prix_devise_en_euros
from inspect import signature as _mutmut_signature
from typing import Annotated
from typing import Callable
from typing import ClassVar


MutantDict = Annotated[dict[str, Callable], "Mutant"]


def _mutmut_trampoline(orig, mutants, call_args, call_kwargs, self_arg = None):
    """Forward call to original or mutated function, depending on the environment"""
    import os
    mutant_under_test = os.environ['MUTANT_UNDER_TEST']
    if mutant_under_test == 'fail':
        from mutmut.__main__ import MutmutProgrammaticFailException
        raise MutmutProgrammaticFailException('Failed programmatically')      
    elif mutant_under_test == 'stats':
        from mutmut.__main__ import record_trampoline_hit
        record_trampoline_hit(orig.__module__ + '.' + orig.__name__)
        result = orig(*call_args, **call_kwargs)
        return result  # for the yield case
    prefix = orig.__module__ + '.' + orig.__name__ + '__mutmut_'
    if not mutant_under_test.startswith(prefix):
        result = orig(*call_args, **call_kwargs)
        return result  # for the yield case
    mutant_name = mutant_under_test.rpartition('.')[-1]
    if self_arg:
        # call to a class method where self is not bound
        result = mutants[mutant_name](self_arg, *call_args, **call_kwargs)
    else:
        result = mutants[mutant_name](*call_args, **call_kwargs)
    return result
from inspect import signature as _mutmut_signature
from typing import Annotated
from typing import Callable
from typing import ClassVar


MutantDict = Annotated[dict[str, Callable], "Mutant"]


def _mutmut_yield_from_trampoline(orig, mutants, call_args, call_kwargs, self_arg = None):
    """Forward call to original or mutated function, depending on the environment"""
    import os
    mutant_under_test = os.environ['MUTANT_UNDER_TEST']
    if mutant_under_test == 'fail':
        from mutmut.__main__ import MutmutProgrammaticFailException
        raise MutmutProgrammaticFailException('Failed programmatically')      
    elif mutant_under_test == 'stats':
        from mutmut.__main__ import record_trampoline_hit
        record_trampoline_hit(orig.__module__ + '.' + orig.__name__)
        result = yield from orig(*call_args, **call_kwargs)
        return result  # for the yield case
    prefix = orig.__module__ + '.' + orig.__name__ + '__mutmut_'
    if not mutant_under_test.startswith(prefix):
        result = yield from orig(*call_args, **call_kwargs)
        return result  # for the yield case
    mutant_name = mutant_under_test.rpartition('.')[-1]
    if self_arg:
        # call to a class method where self is not bound
        result = yield from mutants[mutant_name](self_arg, *call_args, **call_kwargs)
    else:
        result = yield from mutants[mutant_name](*call_args, **call_kwargs)
    return result


class ServiceAffranchissementCommun:
    def xǁServiceAffranchissementCommunǁ__init____mutmut_orig(self, affranchissement: Affranchissement):
        self.affranchissement = affranchissement
    def xǁServiceAffranchissementCommunǁ__init____mutmut_1(self, affranchissement: Affranchissement):
        self.affranchissement = None
    
    xǁServiceAffranchissementCommunǁ__init____mutmut_mutants : ClassVar[MutantDict] = {
    'xǁServiceAffranchissementCommunǁ__init____mutmut_1': xǁServiceAffranchissementCommunǁ__init____mutmut_1
    }
    
    def __init__(self, *args, **kwargs):
        result = _mutmut_trampoline(object.__getattribute__(self, "xǁServiceAffranchissementCommunǁ__init____mutmut_orig"), object.__getattribute__(self, "xǁServiceAffranchissementCommunǁ__init____mutmut_mutants"), args, kwargs, self)
        return result 
    
    __init__.__signature__ = _mutmut_signature(xǁServiceAffranchissementCommunǁ__init____mutmut_orig)
    xǁServiceAffranchissementCommunǁ__init____mutmut_orig.__name__ = 'xǁServiceAffranchissementCommunǁ__init__'

    def xǁServiceAffranchissementCommunǁinformations__mutmut_orig(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_1(self):
        return {
            "XXchamps_manquantsXX": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_2(self):
        return {
            "CHAMPS_MANQUANTS": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_3(self):
        return {
            "Champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_4(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "XXchamps_requisXX": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_5(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "CHAMPS_REQUIS": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_6(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "Champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_7(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "XXcompletXX": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_8(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "COMPLET": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_9(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "Complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_10(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) != 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_11(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 1,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_12(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "XXdonneesXX": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_13(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "DONNEES": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_14(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "Donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_15(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(None),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_16(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "XXlabelXX": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_17(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "LABEL": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_18(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "Label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def xǁServiceAffranchissementCommunǁinformations__mutmut_19(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else 'XXXX'}"
        }
    
    xǁServiceAffranchissementCommunǁinformations__mutmut_mutants : ClassVar[MutantDict] = {
    'xǁServiceAffranchissementCommunǁinformations__mutmut_1': xǁServiceAffranchissementCommunǁinformations__mutmut_1, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_2': xǁServiceAffranchissementCommunǁinformations__mutmut_2, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_3': xǁServiceAffranchissementCommunǁinformations__mutmut_3, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_4': xǁServiceAffranchissementCommunǁinformations__mutmut_4, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_5': xǁServiceAffranchissementCommunǁinformations__mutmut_5, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_6': xǁServiceAffranchissementCommunǁinformations__mutmut_6, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_7': xǁServiceAffranchissementCommunǁinformations__mutmut_7, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_8': xǁServiceAffranchissementCommunǁinformations__mutmut_8, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_9': xǁServiceAffranchissementCommunǁinformations__mutmut_9, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_10': xǁServiceAffranchissementCommunǁinformations__mutmut_10, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_11': xǁServiceAffranchissementCommunǁinformations__mutmut_11, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_12': xǁServiceAffranchissementCommunǁinformations__mutmut_12, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_13': xǁServiceAffranchissementCommunǁinformations__mutmut_13, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_14': xǁServiceAffranchissementCommunǁinformations__mutmut_14, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_15': xǁServiceAffranchissementCommunǁinformations__mutmut_15, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_16': xǁServiceAffranchissementCommunǁinformations__mutmut_16, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_17': xǁServiceAffranchissementCommunǁinformations__mutmut_17, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_18': xǁServiceAffranchissementCommunǁinformations__mutmut_18, 
        'xǁServiceAffranchissementCommunǁinformations__mutmut_19': xǁServiceAffranchissementCommunǁinformations__mutmut_19
    }
    
    def informations(self, *args, **kwargs):
        result = _mutmut_trampoline(object.__getattribute__(self, "xǁServiceAffranchissementCommunǁinformations__mutmut_orig"), object.__getattribute__(self, "xǁServiceAffranchissementCommunǁinformations__mutmut_mutants"), args, kwargs, self)
        return result 
    
    informations.__signature__ = _mutmut_signature(xǁServiceAffranchissementCommunǁinformations__mutmut_orig)
    xǁServiceAffranchissementCommunǁinformations__mutmut_orig.__name__ = 'xǁServiceAffranchissementCommunǁinformations'

    def xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_orig(self):
        """
        Retourne le prix unitaire en euros
        """
        if self.affranchissement.prix_unite_devise is None or self.affranchissement.prix_unite_devise is None:
            return 0
        
        # if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
        #     return 0
        
        return convertir_prix_devise_en_euros(self.affranchissement.devise, self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_1(self):
        """
        Retourne le prix unitaire en euros
        """
        if self.affranchissement.prix_unite_devise is not None or self.affranchissement.prix_unite_devise is None:
            return 0
        
        # if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
        #     return 0
        
        return convertir_prix_devise_en_euros(self.affranchissement.devise, self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_2(self):
        """
        Retourne le prix unitaire en euros
        """
        if self.affranchissement.prix_unite_devise is None and self.affranchissement.prix_unite_devise is None:
            return 0
        
        # if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
        #     return 0
        
        return convertir_prix_devise_en_euros(self.affranchissement.devise, self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_3(self):
        """
        Retourne le prix unitaire en euros
        """
        if self.affranchissement.prix_unite_devise is None or self.affranchissement.prix_unite_devise is not None:
            return 0
        
        # if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
        #     return 0
        
        return convertir_prix_devise_en_euros(self.affranchissement.devise, self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_4(self):
        """
        Retourne le prix unitaire en euros
        """
        if self.affranchissement.prix_unite_devise is None or self.affranchissement.prix_unite_devise is None:
            return 1
        
        # if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
        #     return 0
        
        return convertir_prix_devise_en_euros(self.affranchissement.devise, self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_5(self):
        """
        Retourne le prix unitaire en euros
        """
        if self.affranchissement.prix_unite_devise is None or self.affranchissement.prix_unite_devise is None:
            return 0
        
        # if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
        #     return 0
        
        return convertir_prix_devise_en_euros(None, self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_6(self):
        """
        Retourne le prix unitaire en euros
        """
        if self.affranchissement.prix_unite_devise is None or self.affranchissement.prix_unite_devise is None:
            return 0
        
        # if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
        #     return 0
        
        return convertir_prix_devise_en_euros(self.affranchissement.devise, None)

    def xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_7(self):
        """
        Retourne le prix unitaire en euros
        """
        if self.affranchissement.prix_unite_devise is None or self.affranchissement.prix_unite_devise is None:
            return 0
        
        # if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
        #     return 0
        
        return convertir_prix_devise_en_euros(self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_8(self):
        """
        Retourne le prix unitaire en euros
        """
        if self.affranchissement.prix_unite_devise is None or self.affranchissement.prix_unite_devise is None:
            return 0
        
        # if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
        #     return 0
        
        return convertir_prix_devise_en_euros(self.affranchissement.devise, )
    
    xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_mutants : ClassVar[MutantDict] = {
    'xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_1': xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_1, 
        'xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_2': xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_2, 
        'xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_3': xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_3, 
        'xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_4': xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_4, 
        'xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_5': xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_5, 
        'xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_6': xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_6, 
        'xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_7': xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_7, 
        'xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_8': xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_8
    }
    
    def prix_unite_euros(self, *args, **kwargs):
        result = _mutmut_trampoline(object.__getattribute__(self, "xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_orig"), object.__getattribute__(self, "xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_mutants"), args, kwargs, self)
        return result 
    
    prix_unite_euros.__signature__ = _mutmut_signature(xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_orig)
    xǁServiceAffranchissementCommunǁprix_unite_euros__mutmut_orig.__name__ = 'xǁServiceAffranchissementCommunǁprix_unite_euros'

    def xǁServiceAffranchissementCommunǁvaloriser__mutmut_orig(self):
        raise NotImplementedError("La méthode 'valoriser' doit être implémentée par la classe fille.")

    def xǁServiceAffranchissementCommunǁvaloriser__mutmut_1(self):
        raise NotImplementedError(None)

    def xǁServiceAffranchissementCommunǁvaloriser__mutmut_2(self):
        raise NotImplementedError("XXLa méthode 'valoriser' doit être implémentée par la classe fille.XX")

    def xǁServiceAffranchissementCommunǁvaloriser__mutmut_3(self):
        raise NotImplementedError("la méthode 'valoriser' doit être implémentée par la classe fille.")

    def xǁServiceAffranchissementCommunǁvaloriser__mutmut_4(self):
        raise NotImplementedError("LA MÉTHODE 'VALORISER' DOIT ÊTRE IMPLÉMENTÉE PAR LA CLASSE FILLE.")
    
    xǁServiceAffranchissementCommunǁvaloriser__mutmut_mutants : ClassVar[MutantDict] = {
    'xǁServiceAffranchissementCommunǁvaloriser__mutmut_1': xǁServiceAffranchissementCommunǁvaloriser__mutmut_1, 
        'xǁServiceAffranchissementCommunǁvaloriser__mutmut_2': xǁServiceAffranchissementCommunǁvaloriser__mutmut_2, 
        'xǁServiceAffranchissementCommunǁvaloriser__mutmut_3': xǁServiceAffranchissementCommunǁvaloriser__mutmut_3, 
        'xǁServiceAffranchissementCommunǁvaloriser__mutmut_4': xǁServiceAffranchissementCommunǁvaloriser__mutmut_4
    }
    
    def valoriser(self, *args, **kwargs):
        result = _mutmut_trampoline(object.__getattribute__(self, "xǁServiceAffranchissementCommunǁvaloriser__mutmut_orig"), object.__getattribute__(self, "xǁServiceAffranchissementCommunǁvaloriser__mutmut_mutants"), args, kwargs, self)
        return result 
    
    valoriser.__signature__ = _mutmut_signature(xǁServiceAffranchissementCommunǁvaloriser__mutmut_orig)
    xǁServiceAffranchissementCommunǁvaloriser__mutmut_orig.__name__ = 'xǁServiceAffranchissementCommunǁvaloriser'
    
    def xǁServiceAffranchissementCommunǁchamps_requis__mutmut_orig(self):
        """
        Retourne les champs requis pour l'affranchissement
        """
        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        # # REGLE_METIER: Si l'affranchissement est invalide, on ne demande pas la nature
        # if "nature" in champs_requis and self.affranchissement.statut != ValiditeAffranchissementEnum.VALIDE:
        #     champs_requis.remove("nature")

        return champs_requis
    
    def xǁServiceAffranchissementCommunǁchamps_requis__mutmut_1(self):
        """
        Retourne les champs requis pour l'affranchissement
        """
        champs_requis = None

        # # REGLE_METIER: Si l'affranchissement est invalide, on ne demande pas la nature
        # if "nature" in champs_requis and self.affranchissement.statut != ValiditeAffranchissementEnum.VALIDE:
        #     champs_requis.remove("nature")

        return champs_requis
    
    xǁServiceAffranchissementCommunǁchamps_requis__mutmut_mutants : ClassVar[MutantDict] = {
    'xǁServiceAffranchissementCommunǁchamps_requis__mutmut_1': xǁServiceAffranchissementCommunǁchamps_requis__mutmut_1
    }
    
    def champs_requis(self, *args, **kwargs):
        result = _mutmut_trampoline(object.__getattribute__(self, "xǁServiceAffranchissementCommunǁchamps_requis__mutmut_orig"), object.__getattribute__(self, "xǁServiceAffranchissementCommunǁchamps_requis__mutmut_mutants"), args, kwargs, self)
        return result 
    
    champs_requis.__signature__ = _mutmut_signature(xǁServiceAffranchissementCommunǁchamps_requis__mutmut_orig)
    xǁServiceAffranchissementCommunǁchamps_requis__mutmut_orig.__name__ = 'xǁServiceAffranchissementCommunǁchamps_requis'

    def xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_orig(self):
        """
        Retourne les champs manquants pour l'affranchissement
        """
        champs_requis = self.champs_requis()
        champs_manquants = [champ for champ in champs_requis if getattr(self.affranchissement, champ) is None]

        return champs_manquants

    def xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_1(self):
        """
        Retourne les champs manquants pour l'affranchissement
        """
        champs_requis = None
        champs_manquants = [champ for champ in champs_requis if getattr(self.affranchissement, champ) is None]

        return champs_manquants

    def xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_2(self):
        """
        Retourne les champs manquants pour l'affranchissement
        """
        champs_requis = self.champs_requis()
        champs_manquants = None

        return champs_manquants

    def xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_3(self):
        """
        Retourne les champs manquants pour l'affranchissement
        """
        champs_requis = self.champs_requis()
        champs_manquants = [champ for champ in champs_requis if getattr(None, champ) is None]

        return champs_manquants

    def xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_4(self):
        """
        Retourne les champs manquants pour l'affranchissement
        """
        champs_requis = self.champs_requis()
        champs_manquants = [champ for champ in champs_requis if getattr(self.affranchissement, None) is None]

        return champs_manquants

    def xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_5(self):
        """
        Retourne les champs manquants pour l'affranchissement
        """
        champs_requis = self.champs_requis()
        champs_manquants = [champ for champ in champs_requis if getattr(champ) is None]

        return champs_manquants

    def xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_6(self):
        """
        Retourne les champs manquants pour l'affranchissement
        """
        champs_requis = self.champs_requis()
        champs_manquants = [champ for champ in champs_requis if getattr(self.affranchissement, ) is None]

        return champs_manquants

    def xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_7(self):
        """
        Retourne les champs manquants pour l'affranchissement
        """
        champs_requis = self.champs_requis()
        champs_manquants = [champ for champ in champs_requis if getattr(self.affranchissement, champ) is not None]

        return champs_manquants
    
    xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_mutants : ClassVar[MutantDict] = {
    'xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_1': xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_1, 
        'xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_2': xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_2, 
        'xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_3': xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_3, 
        'xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_4': xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_4, 
        'xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_5': xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_5, 
        'xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_6': xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_6, 
        'xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_7': xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_7
    }
    
    def champs_manquants(self, *args, **kwargs):
        result = _mutmut_trampoline(object.__getattribute__(self, "xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_orig"), object.__getattribute__(self, "xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_mutants"), args, kwargs, self)
        return result 
    
    champs_manquants.__signature__ = _mutmut_signature(xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_orig)
    xǁServiceAffranchissementCommunǁchamps_manquants__mutmut_orig.__name__ = 'xǁServiceAffranchissementCommunǁchamps_manquants'

    def xǁServiceAffranchissementCommunǁupdate__mutmut_orig(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_1(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is not None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_2(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = None

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_3(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get(None) is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_4(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("XXquantiteXX") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_5(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("QUANTITE") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_6(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("Quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_7(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_8(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = None
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_9(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get(None, 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_10(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", None) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_11(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get(1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_12(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", ) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_13(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("XXquantiteXX", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_14(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("QUANTITE", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_15(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("Quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_16(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 2) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_17(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) and 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_18(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 2
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_19(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get(None) is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_20(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("XXstatutXX") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_21(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("STATUT") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_22(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("Statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_23(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_24(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = None

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_25(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get(None, self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_26(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", None)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_27(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get(self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_28(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", )

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_29(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("XXstatutXX", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_30(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("STATUT", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_31(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("Statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_32(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "XXnatureXX" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_33(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "NATURE" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_34(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "Nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_35(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" not in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_36(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut != ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_37(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = None
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_38(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = None
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_39(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 1
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_40(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = None
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_41(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get(None) is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_42(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("XXnatureXX") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_43(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("NATURE") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_44(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("Nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_45(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_46(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = None
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_47(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get(None, self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_48(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", None)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_49(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get(self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_50(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", )
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_51(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("XXnatureXX", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_52(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("NATURE", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_53(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("Nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_54(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = None
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_55(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = None
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_56(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite and 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_57(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 1
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_58(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = None

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_59(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get(None) is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_60(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("XXprix_unite_deviseXX") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_61(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("PRIX_UNITE_DEVISE") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_62(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("Prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_63(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_64(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None or self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_65(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature != NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_66(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = None

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_67(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get(None, self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_68(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", None)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_69(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get(self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_70(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", )

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_71(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("XXprix_unite_deviseXX", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_72(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("PRIX_UNITE_DEVISE", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_73(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("Prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_74(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get(None) is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_75(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("XXprix_unite_deviseXX") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_76(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("PRIX_UNITE_DEVISE") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_77(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("Prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_78(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_79(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None or "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_80(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "XXprix_unite_deviseXX" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_81(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "PRIX_UNITE_DEVISE" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_82(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "Prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_83(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" not in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_84(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = None

    def xǁServiceAffranchissementCommunǁupdate__mutmut_85(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get(None, self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_86(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", None)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_87(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get(self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_88(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", )

    def xǁServiceAffranchissementCommunǁupdate__mutmut_89(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("XXprix_unite_deviseXX", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_90(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("PRIX_UNITE_DEVISE", self.affranchissement.prix_unite_devise)

    def xǁServiceAffranchissementCommunǁupdate__mutmut_91(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("Prix_unite_devise", self.affranchissement.prix_unite_devise)
    
    xǁServiceAffranchissementCommunǁupdate__mutmut_mutants : ClassVar[MutantDict] = {
    'xǁServiceAffranchissementCommunǁupdate__mutmut_1': xǁServiceAffranchissementCommunǁupdate__mutmut_1, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_2': xǁServiceAffranchissementCommunǁupdate__mutmut_2, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_3': xǁServiceAffranchissementCommunǁupdate__mutmut_3, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_4': xǁServiceAffranchissementCommunǁupdate__mutmut_4, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_5': xǁServiceAffranchissementCommunǁupdate__mutmut_5, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_6': xǁServiceAffranchissementCommunǁupdate__mutmut_6, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_7': xǁServiceAffranchissementCommunǁupdate__mutmut_7, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_8': xǁServiceAffranchissementCommunǁupdate__mutmut_8, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_9': xǁServiceAffranchissementCommunǁupdate__mutmut_9, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_10': xǁServiceAffranchissementCommunǁupdate__mutmut_10, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_11': xǁServiceAffranchissementCommunǁupdate__mutmut_11, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_12': xǁServiceAffranchissementCommunǁupdate__mutmut_12, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_13': xǁServiceAffranchissementCommunǁupdate__mutmut_13, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_14': xǁServiceAffranchissementCommunǁupdate__mutmut_14, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_15': xǁServiceAffranchissementCommunǁupdate__mutmut_15, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_16': xǁServiceAffranchissementCommunǁupdate__mutmut_16, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_17': xǁServiceAffranchissementCommunǁupdate__mutmut_17, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_18': xǁServiceAffranchissementCommunǁupdate__mutmut_18, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_19': xǁServiceAffranchissementCommunǁupdate__mutmut_19, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_20': xǁServiceAffranchissementCommunǁupdate__mutmut_20, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_21': xǁServiceAffranchissementCommunǁupdate__mutmut_21, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_22': xǁServiceAffranchissementCommunǁupdate__mutmut_22, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_23': xǁServiceAffranchissementCommunǁupdate__mutmut_23, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_24': xǁServiceAffranchissementCommunǁupdate__mutmut_24, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_25': xǁServiceAffranchissementCommunǁupdate__mutmut_25, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_26': xǁServiceAffranchissementCommunǁupdate__mutmut_26, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_27': xǁServiceAffranchissementCommunǁupdate__mutmut_27, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_28': xǁServiceAffranchissementCommunǁupdate__mutmut_28, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_29': xǁServiceAffranchissementCommunǁupdate__mutmut_29, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_30': xǁServiceAffranchissementCommunǁupdate__mutmut_30, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_31': xǁServiceAffranchissementCommunǁupdate__mutmut_31, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_32': xǁServiceAffranchissementCommunǁupdate__mutmut_32, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_33': xǁServiceAffranchissementCommunǁupdate__mutmut_33, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_34': xǁServiceAffranchissementCommunǁupdate__mutmut_34, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_35': xǁServiceAffranchissementCommunǁupdate__mutmut_35, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_36': xǁServiceAffranchissementCommunǁupdate__mutmut_36, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_37': xǁServiceAffranchissementCommunǁupdate__mutmut_37, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_38': xǁServiceAffranchissementCommunǁupdate__mutmut_38, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_39': xǁServiceAffranchissementCommunǁupdate__mutmut_39, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_40': xǁServiceAffranchissementCommunǁupdate__mutmut_40, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_41': xǁServiceAffranchissementCommunǁupdate__mutmut_41, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_42': xǁServiceAffranchissementCommunǁupdate__mutmut_42, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_43': xǁServiceAffranchissementCommunǁupdate__mutmut_43, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_44': xǁServiceAffranchissementCommunǁupdate__mutmut_44, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_45': xǁServiceAffranchissementCommunǁupdate__mutmut_45, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_46': xǁServiceAffranchissementCommunǁupdate__mutmut_46, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_47': xǁServiceAffranchissementCommunǁupdate__mutmut_47, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_48': xǁServiceAffranchissementCommunǁupdate__mutmut_48, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_49': xǁServiceAffranchissementCommunǁupdate__mutmut_49, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_50': xǁServiceAffranchissementCommunǁupdate__mutmut_50, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_51': xǁServiceAffranchissementCommunǁupdate__mutmut_51, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_52': xǁServiceAffranchissementCommunǁupdate__mutmut_52, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_53': xǁServiceAffranchissementCommunǁupdate__mutmut_53, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_54': xǁServiceAffranchissementCommunǁupdate__mutmut_54, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_55': xǁServiceAffranchissementCommunǁupdate__mutmut_55, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_56': xǁServiceAffranchissementCommunǁupdate__mutmut_56, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_57': xǁServiceAffranchissementCommunǁupdate__mutmut_57, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_58': xǁServiceAffranchissementCommunǁupdate__mutmut_58, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_59': xǁServiceAffranchissementCommunǁupdate__mutmut_59, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_60': xǁServiceAffranchissementCommunǁupdate__mutmut_60, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_61': xǁServiceAffranchissementCommunǁupdate__mutmut_61, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_62': xǁServiceAffranchissementCommunǁupdate__mutmut_62, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_63': xǁServiceAffranchissementCommunǁupdate__mutmut_63, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_64': xǁServiceAffranchissementCommunǁupdate__mutmut_64, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_65': xǁServiceAffranchissementCommunǁupdate__mutmut_65, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_66': xǁServiceAffranchissementCommunǁupdate__mutmut_66, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_67': xǁServiceAffranchissementCommunǁupdate__mutmut_67, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_68': xǁServiceAffranchissementCommunǁupdate__mutmut_68, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_69': xǁServiceAffranchissementCommunǁupdate__mutmut_69, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_70': xǁServiceAffranchissementCommunǁupdate__mutmut_70, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_71': xǁServiceAffranchissementCommunǁupdate__mutmut_71, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_72': xǁServiceAffranchissementCommunǁupdate__mutmut_72, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_73': xǁServiceAffranchissementCommunǁupdate__mutmut_73, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_74': xǁServiceAffranchissementCommunǁupdate__mutmut_74, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_75': xǁServiceAffranchissementCommunǁupdate__mutmut_75, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_76': xǁServiceAffranchissementCommunǁupdate__mutmut_76, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_77': xǁServiceAffranchissementCommunǁupdate__mutmut_77, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_78': xǁServiceAffranchissementCommunǁupdate__mutmut_78, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_79': xǁServiceAffranchissementCommunǁupdate__mutmut_79, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_80': xǁServiceAffranchissementCommunǁupdate__mutmut_80, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_81': xǁServiceAffranchissementCommunǁupdate__mutmut_81, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_82': xǁServiceAffranchissementCommunǁupdate__mutmut_82, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_83': xǁServiceAffranchissementCommunǁupdate__mutmut_83, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_84': xǁServiceAffranchissementCommunǁupdate__mutmut_84, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_85': xǁServiceAffranchissementCommunǁupdate__mutmut_85, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_86': xǁServiceAffranchissementCommunǁupdate__mutmut_86, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_87': xǁServiceAffranchissementCommunǁupdate__mutmut_87, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_88': xǁServiceAffranchissementCommunǁupdate__mutmut_88, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_89': xǁServiceAffranchissementCommunǁupdate__mutmut_89, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_90': xǁServiceAffranchissementCommunǁupdate__mutmut_90, 
        'xǁServiceAffranchissementCommunǁupdate__mutmut_91': xǁServiceAffranchissementCommunǁupdate__mutmut_91
    }
    
    def update(self, *args, **kwargs):
        result = _mutmut_trampoline(object.__getattribute__(self, "xǁServiceAffranchissementCommunǁupdate__mutmut_orig"), object.__getattribute__(self, "xǁServiceAffranchissementCommunǁupdate__mutmut_mutants"), args, kwargs, self)
        return result 
    
    update.__signature__ = _mutmut_signature(xǁServiceAffranchissementCommunǁupdate__mutmut_orig)
    xǁServiceAffranchissementCommunǁupdate__mutmut_orig.__name__ = 'xǁServiceAffranchissementCommunǁupdate'

    @classmethod
    def donnees(cls, code=None):
        """
        Retourne les données de l'affranchissement
        """
        return {}

import re
from services.affranchissements.commun import ServiceAffranchissementCommun
from constants.enumerations import TypeAffranchissementEnum, DeviseEnum, TypeRegleMetierEnum
from models.business import RegleMetier
from sqlalchemy import JSO<PERSON>, cast, func
from sqlmodel import select
from constants.variables import CONSTANTES_PRIX_AFFRANCHISSEMENT
from inspect import signature as _mutmut_signature
from typing import Annotated
from typing import Callable
from typing import ClassVar


MutantDict = Annotated[dict[str, Callable], "Mutant"]


def _mutmut_trampoline(orig, mutants, call_args, call_kwargs, self_arg = None):
    """Forward call to original or mutated function, depending on the environment"""
    import os
    mutant_under_test = os.environ['MUTANT_UNDER_TEST']
    if mutant_under_test == 'fail':
        from mutmut.__main__ import MutmutProgrammaticFailException
        raise MutmutProgrammaticFailException('Failed programmatically')      
    elif mutant_under_test == 'stats':
        from mutmut.__main__ import record_trampoline_hit
        record_trampoline_hit(orig.__module__ + '.' + orig.__name__)
        result = orig(*call_args, **call_kwargs)
        return result  # for the yield case
    prefix = orig.__module__ + '.' + orig.__name__ + '__mutmut_'
    if not mutant_under_test.startswith(prefix):
        result = orig(*call_args, **call_kwargs)
        return result  # for the yield case
    mutant_name = mutant_under_test.rpartition('.')[-1]
    if self_arg:
        # call to a class method where self is not bound
        result = mutants[mutant_name](self_arg, *call_args, **call_kwargs)
    else:
        result = mutants[mutant_name](*call_args, **call_kwargs)
    return result
from inspect import signature as _mutmut_signature
from typing import Annotated
from typing import Callable
from typing import ClassVar


MutantDict = Annotated[dict[str, Callable], "Mutant"]


def _mutmut_yield_from_trampoline(orig, mutants, call_args, call_kwargs, self_arg = None):
    """Forward call to original or mutated function, depending on the environment"""
    import os
    mutant_under_test = os.environ['MUTANT_UNDER_TEST']
    if mutant_under_test == 'fail':
        from mutmut.__main__ import MutmutProgrammaticFailException
        raise MutmutProgrammaticFailException('Failed programmatically')      
    elif mutant_under_test == 'stats':
        from mutmut.__main__ import record_trampoline_hit
        record_trampoline_hit(orig.__module__ + '.' + orig.__name__)
        result = yield from orig(*call_args, **call_kwargs)
        return result  # for the yield case
    prefix = orig.__module__ + '.' + orig.__name__ + '__mutmut_'
    if not mutant_under_test.startswith(prefix):
        result = yield from orig(*call_args, **call_kwargs)
        return result  # for the yield case
    mutant_name = mutant_under_test.rpartition('.')[-1]
    if self_arg:
        # call to a class method where self is not bound
        result = yield from mutants[mutant_name](self_arg, *call_args, **call_kwargs)
    else:
        result = yield from mutants[mutant_name](*call_args, **call_kwargs)
    return result


class ServiceAffranchissementS10(ServiceAffranchissementCommun):
    """
    Service d'affranchissement pour les S10
    """

    REGEX = r"^([A-Za-z]{2})([0-9]{8})([0-9]{1})([A-Za-z]{2})$"
    TYPE = TypeAffranchissementEnum.S10
    
    @classmethod
    def donnees(cls, code=None):
        """
        Extrait les données du code S10 en utilisant la REGEX définie dans la classe
        """
        match = re.match(cls.REGEX, code)

        if not match:
            return None

        cpt = 1
        content = {
            "service": match.group(cpt).upper(),
            "number": match.group(cpt + 1),
            "key": match.group(cpt + 2),
            "country": match.group(cpt + 3).upper(),
        }

        id = f"{content['service']}{content['number']}{content['key']}{content['country']}"
        segmented = f"{content['service']} {content['number']} {content['key']} {content['country']}"

        return {"id": id, 
                "segmented": segmented, 
                "content": content}

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_orig(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_1(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = None
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_2(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 1
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_3(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = None

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_4(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = None

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_5(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_6(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence and not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_7(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_8(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = None
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_9(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get(None)
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_10(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("XXvaleurXX")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_11(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("VALEUR")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_12(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("Valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_13(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = None
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_14(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(None)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_15(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float > 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_16(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 1:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_17(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = None
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_18(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").upper() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_19(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get(None, "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_20(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", None).lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_21(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_22(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", ).lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_23(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("XXemetteurXX", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_24(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("EMETTEUR", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_25(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("Emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_26(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "XXXX").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_27(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() != "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_28(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "XXphilaposteXX" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_29(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "PHILAPOSTE" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_30(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "Philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_31(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" or self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_32(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["XXcontentXX"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_33(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["CONTENT"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_34(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["Content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_35(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["XXserviceXX"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_36(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["SERVICE"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_37(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["Service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_38(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] != "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_39(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "XXLEXX":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_40(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "le":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_41(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "Le":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def xǁServiceAffranchissementS10ǁvaloriser__mutmut_42(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = None
    
    xǁServiceAffranchissementS10ǁvaloriser__mutmut_mutants : ClassVar[MutantDict] = {
    'xǁServiceAffranchissementS10ǁvaloriser__mutmut_1': xǁServiceAffranchissementS10ǁvaloriser__mutmut_1, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_2': xǁServiceAffranchissementS10ǁvaloriser__mutmut_2, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_3': xǁServiceAffranchissementS10ǁvaloriser__mutmut_3, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_4': xǁServiceAffranchissementS10ǁvaloriser__mutmut_4, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_5': xǁServiceAffranchissementS10ǁvaloriser__mutmut_5, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_6': xǁServiceAffranchissementS10ǁvaloriser__mutmut_6, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_7': xǁServiceAffranchissementS10ǁvaloriser__mutmut_7, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_8': xǁServiceAffranchissementS10ǁvaloriser__mutmut_8, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_9': xǁServiceAffranchissementS10ǁvaloriser__mutmut_9, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_10': xǁServiceAffranchissementS10ǁvaloriser__mutmut_10, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_11': xǁServiceAffranchissementS10ǁvaloriser__mutmut_11, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_12': xǁServiceAffranchissementS10ǁvaloriser__mutmut_12, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_13': xǁServiceAffranchissementS10ǁvaloriser__mutmut_13, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_14': xǁServiceAffranchissementS10ǁvaloriser__mutmut_14, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_15': xǁServiceAffranchissementS10ǁvaloriser__mutmut_15, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_16': xǁServiceAffranchissementS10ǁvaloriser__mutmut_16, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_17': xǁServiceAffranchissementS10ǁvaloriser__mutmut_17, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_18': xǁServiceAffranchissementS10ǁvaloriser__mutmut_18, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_19': xǁServiceAffranchissementS10ǁvaloriser__mutmut_19, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_20': xǁServiceAffranchissementS10ǁvaloriser__mutmut_20, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_21': xǁServiceAffranchissementS10ǁvaloriser__mutmut_21, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_22': xǁServiceAffranchissementS10ǁvaloriser__mutmut_22, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_23': xǁServiceAffranchissementS10ǁvaloriser__mutmut_23, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_24': xǁServiceAffranchissementS10ǁvaloriser__mutmut_24, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_25': xǁServiceAffranchissementS10ǁvaloriser__mutmut_25, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_26': xǁServiceAffranchissementS10ǁvaloriser__mutmut_26, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_27': xǁServiceAffranchissementS10ǁvaloriser__mutmut_27, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_28': xǁServiceAffranchissementS10ǁvaloriser__mutmut_28, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_29': xǁServiceAffranchissementS10ǁvaloriser__mutmut_29, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_30': xǁServiceAffranchissementS10ǁvaloriser__mutmut_30, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_31': xǁServiceAffranchissementS10ǁvaloriser__mutmut_31, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_32': xǁServiceAffranchissementS10ǁvaloriser__mutmut_32, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_33': xǁServiceAffranchissementS10ǁvaloriser__mutmut_33, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_34': xǁServiceAffranchissementS10ǁvaloriser__mutmut_34, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_35': xǁServiceAffranchissementS10ǁvaloriser__mutmut_35, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_36': xǁServiceAffranchissementS10ǁvaloriser__mutmut_36, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_37': xǁServiceAffranchissementS10ǁvaloriser__mutmut_37, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_38': xǁServiceAffranchissementS10ǁvaloriser__mutmut_38, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_39': xǁServiceAffranchissementS10ǁvaloriser__mutmut_39, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_40': xǁServiceAffranchissementS10ǁvaloriser__mutmut_40, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_41': xǁServiceAffranchissementS10ǁvaloriser__mutmut_41, 
        'xǁServiceAffranchissementS10ǁvaloriser__mutmut_42': xǁServiceAffranchissementS10ǁvaloriser__mutmut_42
    }
    
    def valoriser(self, *args, **kwargs):
        result = _mutmut_trampoline(object.__getattribute__(self, "xǁServiceAffranchissementS10ǁvaloriser__mutmut_orig"), object.__getattribute__(self, "xǁServiceAffranchissementS10ǁvaloriser__mutmut_mutants"), args, kwargs, self)
        return result 
    
    valoriser.__signature__ = _mutmut_signature(xǁServiceAffranchissementS10ǁvaloriser__mutmut_orig)
    xǁServiceAffranchissementS10ǁvaloriser__mutmut_orig.__name__ = 'xǁServiceAffranchissementS10ǁvaloriser'

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_orig(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_1(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = None

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_2(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is not None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_3(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = None
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_4(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["XXcontentXX"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_5(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["CONTENT"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_6(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["Content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_7(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["XXserviceXX"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_8(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["SERVICE"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_9(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["Service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_10(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = None

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_11(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["XXcontentXX"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_12(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["CONTENT"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_13(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["Content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_14(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["XXnumberXX"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_15(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["NUMBER"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_16(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["Number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_17(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = None
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_18(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                None,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_19(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                None,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_20(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                None,
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_21(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                None,
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_22(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                None
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_23(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_24(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_25(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_26(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_27(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_28(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(None).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_29(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement != TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_30(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle != TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_31(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(None, 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_32(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), None) == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_33(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text('service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_34(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), ) == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_35(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(None, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_36(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, None), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_37(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_38(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, ), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_39(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'XXserviceXX') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_40(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'SERVICE') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_41(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'Service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_42(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') != service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_43(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(None, 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_44(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), None) <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_45(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text('debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_46(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), ) <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_47(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(None, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_48(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, None), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_49(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_50(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, ), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_51(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'XXdebutXX') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_52(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'DEBUT') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_53(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'Debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_54(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') < number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_55(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(None, 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_56(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), None) >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_57(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text('fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_58(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), ) >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_59(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(None, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_60(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, None), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_61(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_62(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, ), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_63(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'XXfinXX') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_64(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'FIN') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_65(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'Fin') >= number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_66(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') > number)
            )
            
            return session.exec(statement).first()

    def xǁServiceAffranchissementS10ǁsequence_associee__mutmut_67(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(None).first()
    
    xǁServiceAffranchissementS10ǁsequence_associee__mutmut_mutants : ClassVar[MutantDict] = {
    'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_1': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_1, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_2': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_2, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_3': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_3, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_4': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_4, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_5': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_5, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_6': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_6, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_7': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_7, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_8': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_8, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_9': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_9, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_10': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_10, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_11': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_11, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_12': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_12, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_13': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_13, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_14': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_14, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_15': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_15, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_16': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_16, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_17': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_17, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_18': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_18, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_19': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_19, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_20': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_20, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_21': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_21, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_22': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_22, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_23': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_23, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_24': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_24, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_25': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_25, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_26': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_26, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_27': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_27, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_28': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_28, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_29': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_29, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_30': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_30, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_31': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_31, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_32': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_32, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_33': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_33, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_34': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_34, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_35': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_35, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_36': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_36, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_37': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_37, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_38': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_38, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_39': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_39, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_40': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_40, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_41': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_41, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_42': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_42, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_43': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_43, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_44': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_44, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_45': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_45, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_46': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_46, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_47': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_47, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_48': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_48, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_49': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_49, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_50': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_50, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_51': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_51, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_52': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_52, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_53': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_53, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_54': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_54, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_55': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_55, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_56': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_56, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_57': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_57, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_58': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_58, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_59': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_59, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_60': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_60, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_61': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_61, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_62': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_62, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_63': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_63, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_64': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_64, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_65': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_65, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_66': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_66, 
        'xǁServiceAffranchissementS10ǁsequence_associee__mutmut_67': xǁServiceAffranchissementS10ǁsequence_associee__mutmut_67
    }
    
    def sequence_associee(self, *args, **kwargs):
        result = _mutmut_trampoline(object.__getattribute__(self, "xǁServiceAffranchissementS10ǁsequence_associee__mutmut_orig"), object.__getattribute__(self, "xǁServiceAffranchissementS10ǁsequence_associee__mutmut_mutants"), args, kwargs, self)
        return result 
    
    sequence_associee.__signature__ = _mutmut_signature(xǁServiceAffranchissementS10ǁsequence_associee__mutmut_orig)
    xǁServiceAffranchissementS10ǁsequence_associee__mutmut_orig.__name__ = 'xǁServiceAffranchissementS10ǁsequence_associee'

    def xǁServiceAffranchissementS10ǁchamps_requis__mutmut_orig(self):
        if len(self.affranchissement.code or "") == 0:
            return ["nature"]
        
        return []

    def xǁServiceAffranchissementS10ǁchamps_requis__mutmut_1(self):
        if len(self.affranchissement.code or "") != 0:
            return ["nature"]
        
        return []

    def xǁServiceAffranchissementS10ǁchamps_requis__mutmut_2(self):
        if len(self.affranchissement.code or "") == 1:
            return ["nature"]
        
        return []

    def xǁServiceAffranchissementS10ǁchamps_requis__mutmut_3(self):
        if len(self.affranchissement.code or "") == 0:
            return ["XXnatureXX"]
        
        return []

    def xǁServiceAffranchissementS10ǁchamps_requis__mutmut_4(self):
        if len(self.affranchissement.code or "") == 0:
            return ["NATURE"]
        
        return []

    def xǁServiceAffranchissementS10ǁchamps_requis__mutmut_5(self):
        if len(self.affranchissement.code or "") == 0:
            return ["Nature"]
        
        return []
    
    xǁServiceAffranchissementS10ǁchamps_requis__mutmut_mutants : ClassVar[MutantDict] = {
    'xǁServiceAffranchissementS10ǁchamps_requis__mutmut_1': xǁServiceAffranchissementS10ǁchamps_requis__mutmut_1, 
        'xǁServiceAffranchissementS10ǁchamps_requis__mutmut_2': xǁServiceAffranchissementS10ǁchamps_requis__mutmut_2, 
        'xǁServiceAffranchissementS10ǁchamps_requis__mutmut_3': xǁServiceAffranchissementS10ǁchamps_requis__mutmut_3, 
        'xǁServiceAffranchissementS10ǁchamps_requis__mutmut_4': xǁServiceAffranchissementS10ǁchamps_requis__mutmut_4, 
        'xǁServiceAffranchissementS10ǁchamps_requis__mutmut_5': xǁServiceAffranchissementS10ǁchamps_requis__mutmut_5
    }
    
    def champs_requis(self, *args, **kwargs):
        result = _mutmut_trampoline(object.__getattribute__(self, "xǁServiceAffranchissementS10ǁchamps_requis__mutmut_orig"), object.__getattribute__(self, "xǁServiceAffranchissementS10ǁchamps_requis__mutmut_mutants"), args, kwargs, self)
        return result 
    
    champs_requis.__signature__ = _mutmut_signature(xǁServiceAffranchissementS10ǁchamps_requis__mutmut_orig)
    xǁServiceAffranchissementS10ǁchamps_requis__mutmut_orig.__name__ = 'xǁServiceAffranchissementS10ǁchamps_requis'

from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select, func
from typing import List
from api.deps import SessionDep, CurrentAdminUser
from models import RegleMetier
from models.public import PaginatedResponse
from math import ceil


router = APIRouter()
from inspect import signature as _mutmut_signature
from typing import Annotated
from typing import Callable
from typing import ClassVar


MutantDict = Annotated[dict[str, Callable], "Mutant"]


def _mutmut_trampoline(orig, mutants, call_args, call_kwargs, self_arg = None):
    """Forward call to original or mutated function, depending on the environment"""
    import os
    mutant_under_test = os.environ['MUTANT_UNDER_TEST']
    if mutant_under_test == 'fail':
        from mutmut.__main__ import MutmutProgrammaticFailException
        raise MutmutProgrammaticFailException('Failed programmatically')      
    elif mutant_under_test == 'stats':
        from mutmut.__main__ import record_trampoline_hit
        record_trampoline_hit(orig.__module__ + '.' + orig.__name__)
        result = orig(*call_args, **call_kwargs)
        return result  # for the yield case
    prefix = orig.__module__ + '.' + orig.__name__ + '__mutmut_'
    if not mutant_under_test.startswith(prefix):
        result = orig(*call_args, **call_kwargs)
        return result  # for the yield case
    mutant_name = mutant_under_test.rpartition('.')[-1]
    if self_arg:
        # call to a class method where self is not bound
        result = mutants[mutant_name](self_arg, *call_args, **call_kwargs)
    else:
        result = mutants[mutant_name](*call_args, **call_kwargs)
    return result
from inspect import signature as _mutmut_signature
from typing import Annotated
from typing import Callable
from typing import ClassVar


MutantDict = Annotated[dict[str, Callable], "Mutant"]


def _mutmut_yield_from_trampoline(orig, mutants, call_args, call_kwargs, self_arg = None):
    """Forward call to original or mutated function, depending on the environment"""
    import os
    mutant_under_test = os.environ['MUTANT_UNDER_TEST']
    if mutant_under_test == 'fail':
        from mutmut.__main__ import MutmutProgrammaticFailException
        raise MutmutProgrammaticFailException('Failed programmatically')      
    elif mutant_under_test == 'stats':
        from mutmut.__main__ import record_trampoline_hit
        record_trampoline_hit(orig.__module__ + '.' + orig.__name__)
        result = yield from orig(*call_args, **call_kwargs)
        return result  # for the yield case
    prefix = orig.__module__ + '.' + orig.__name__ + '__mutmut_'
    if not mutant_under_test.startswith(prefix):
        result = yield from orig(*call_args, **call_kwargs)
        return result  # for the yield case
    mutant_name = mutant_under_test.rpartition('.')[-1]
    if self_arg:
        # call to a class method where self is not bound
        result = yield from mutants[mutant_name](self_arg, *call_args, **call_kwargs)
    else:
        result = yield from mutants[mutant_name](*call_args, **call_kwargs)
    return result

@router.post("/", response_model=RegleMetier)
def create_regle_metier(
    *,
    session: SessionDep,
    current_admin: CurrentAdminUser,
    regle_metier: RegleMetier
) -> RegleMetier:
    """
    Créer une nouvelle règle métier (admin uniquement)
    """
    session.add(regle_metier)
    session.commit()
    session.refresh(regle_metier)
    return regle_metier

@router.get("/", response_model=PaginatedResponse[RegleMetier])
def read_regles_metier(
    *,
    session: SessionDep,
    current_admin: CurrentAdminUser,
    skip: int = 0,
    limit: int = 10,
    search: str | None = None,
) -> PaginatedResponse[RegleMetier]:
    """
    Récupérer la liste paginée des règles métier (admin uniquement)
    """
    # ---- 1. Base query -------------------------------------------------------
    base_query = select(RegleMetier)
    if search:
        like = f"%{search}%"
        base_query = base_query.where(RegleMetier.cle.ilike(like))

    # ---- 2. Total items ------------------------------------------------------
    total_items: int = session.exec(
        select(func.count()).select_from(base_query.subquery())
    ).one()

    # ---- 3. Slice paginée ----------------------------------------------------
    items: list[RegleMetier] = session.exec(
           base_query.order_by(RegleMetier.cle.asc(), RegleMetier.id.asc())  
            .offset(skip)
            .limit(limit)
    ).all()

    # ---- 4. Response ---------------------------------------------------------
    return PaginatedResponse[RegleMetier](
        items=items,
        current_page=(skip // limit) + 1,
        page_size=limit,
        total_items=total_items,
        total_pages=ceil(total_items / limit) if limit else 1,
    )


@router.get("/{regle_metier_id}", response_model=RegleMetier)
def read_regle_metier(
    *,
    session: SessionDep,
    current_admin: CurrentAdminUser,
    regle_metier_id: int
) -> RegleMetier:
    """
    Récupérer une règle métier par ID (admin uniquement)
    """
    regle_metier = session.get(RegleMetier, regle_metier_id)
    if not regle_metier:
        raise HTTPException(status_code=404, detail="Règle métier non trouvée")
    return regle_metier

@router.put("/{regle_metier_id}", response_model=RegleMetier)
def update_regle_metier(
    *,
    session: SessionDep,
    current_admin: CurrentAdminUser,
    regle_metier_id: int,
    regle_metier: RegleMetier
) -> RegleMetier:
    """
    Mettre à jour une règle métier existante (admin uniquement)
    """
    db_regle_metier = session.get(RegleMetier, regle_metier_id)
    if not db_regle_metier:
        raise HTTPException(status_code=404, detail="Règle métier non trouvée")
    
    for key, value in regle_metier.dict(exclude_unset=True).items():
        setattr(db_regle_metier, key, value)
    
    session.add(db_regle_metier)
    session.commit()
    session.refresh(db_regle_metier)
    return db_regle_metier

@router.delete("/{regle_metier_id}", response_model=RegleMetier)
def delete_regle_metier(
    *,
    session: SessionDep,
    current_admin: CurrentAdminUser,
    regle_metier_id: int
) -> RegleMetier:
    """
    Supprimer une règle métier (admin uniquement)
    """
    regle_metier = session.get(RegleMetier, regle_metier_id)
    if not regle_metier:
        raise HTTPException(status_code=404, detail="Règle métier non trouvée")
    
    session.delete(regle_metier)
    session.commit()
    return regle_metier
import pytest
from fastapi.testclient import TestClient
from sqlmodel import Session, select
from models.business import <PERSON>asi<PERSON>, Site, Enveloppe, User
from constants.enumerations import DestinationEnveloppeEnum
from models.users import UserRole
from tests.conftest import client as _client


class TestCasiersDeplacementAPI:
    """Tests pour l'API de déplacement de plis entre casiers"""

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.client = _client(user, "/casiers/")
        self.admin_client = _client(admin_user, "/casiers/")

    @pytest.fixture
    def site_source(self, db):
        site = Site(nom="Site Source")
        db.add(site)
        db.commit()
        db.refresh(site)
        return site

    @pytest.fixture
    def site_destination(self, db):
        site = Site(nom="Site Destination")
        db.add(site)
        db.commit()
        db.refresh(site)
        return site

    def test_deplacer_plis_success(self, db, site_source, site_destination, user):
        """Test du déplacement réussi via l'API"""
        
        # Créer casier source avec des enveloppes
        casier_source = Casier(
            site_id=site_source.id,
            numero="API-SOURCE-1",
        )
        db.add(casier_source)
        
        # Créer casier destination
        casier_destination = Casier(
            site_id=site_destination.id,
            numero="API-DEST-1",
        )
        db.add(casier_destination)
        db.commit()
        
        # Créer des enveloppes dans le casier source
        for i in range(2):
            enveloppe = Enveloppe(
                casier_id=casier_source.id,
                site_id=site_source.id,
                user_id=user.id,
                destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
                poids=100.0
            )
            db.add(enveloppe)
        db.commit()
        
        # Effectuer la requête
        response = self.admin_client.post(
            "/deplacer-plis",
            json={
                "casier_source_id": casier_source.id,
                "casier_destination_id": casier_destination.id
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["nb_plis_deplaces"] == 2
        assert data["casier_source"]["id"] == casier_source.id
        assert data["casier_destination"]["id"] == casier_destination.id
        assert "Déplacement réussi" in data["message"]

        # Vérifier que les enveloppes ont bien été déplacées
        enveloppes = db.exec(select(Enveloppe).where(Enveloppe.casier_id == casier_destination.id)).all()
        assert len(enveloppes) == 2
        for enveloppe in enveloppes:
            assert enveloppe.site_id == site_destination.id

    def test_deplacer_plis_casier_inexistant(self, db, site_destination):
        """Test avec un casier inexistant"""
        
        casier_destination = Casier(
            site_id=site_destination.id,
            numero="API-DEST-1"
        )
        db.add(casier_destination)
        db.commit()
        
        response = self.admin_client.post(
            "/deplacer-plis",
            json={
                "casier_source_id": 999,
                "casier_destination_id": casier_destination.id
            }
        )
        
        assert response.status_code == 404
        assert "Casier source non trouvé" in response.json()["detail"]

    def test_deplacer_plis_meme_site(self, db, site_source):
        """Test avec des casiers sur le même site"""
        
        casier_source = Casier(
            site_id=site_source.id,
            numero="API-SOURCE-1",
        )
        casier_destination = Casier(
            site_id=site_source.id,
            numero="API-DEST-1",
        )
        db.add(casier_source)
        db.add(casier_destination)
        db.commit()
        
        response = self.admin_client.post(
            "/deplacer-plis",
            json={
                "casier_source_id": casier_source.id,
                "casier_destination_id": casier_destination.id
            }
        )
        
        assert response.status_code == 400
        assert "Les casiers doivent être sur des sites différents" in response.json()["detail"]

    def test_deplacer_plis_unauthorized(self, db, site_source, site_destination):
        """Test avec un utilisateur non autorisé"""
        
        casier_source = Casier(
            site_id=site_source.id,
            numero="API-SOURCE-1",
        )
        casier_destination = Casier(
            site_id=site_destination.id,
            numero="API-DEST-1",
        )
        db.add(casier_source)
        db.add(casier_destination)
        db.commit()
        
        response = self.client.post(
            "/deplacer-plis",
            json={
                "casier_source_id": casier_source.id,
                "casier_destination_id": casier_destination.id
            }
        )
        
        assert response.status_code in [401, 403]
        assert "L'utilisateur n'a pas les privilèges suffisants : manager" in response.json()["detail"]

    def test_deplacer_plis_donnees_invalides(self):
        """Test avec des données de requête invalides"""
        
        response = self.admin_client.post(
            "/deplacer-plis",
            json={
                "casier_source_id": "invalid",
                "casier_destination_id": None
            }
        )
        
        assert response.status_code == 422

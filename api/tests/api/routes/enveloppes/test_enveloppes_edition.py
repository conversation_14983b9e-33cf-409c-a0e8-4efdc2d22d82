from tests.conftest import client as _client
import pytest
from sqlmodel import Session, select
from models.business import Enveloppe, Affranchissement, PhotoEnveloppe, Site, Destination, Expediteur
from datetime import datetime
from tests.fixtures import enveloppe, S10, user
from constants.enumerations import CategorieAffranchissementEnum, DeviseEnum, StatutEnveloppeEnum, TypeAffranchissementEnum, DestinationEnveloppeEnum, StatutVerificationEnum, DestinationEnveloppeEnum
from fastapi import UploadFile
from unittest.mock import patch
import io
from tests.fixtures.users import create_user, get_password_hash
from models.users import UserRole, User
from tests.mocks.storage_service import patch_blob_operations
from tests.mocks.ssu_api import patch_ssu_tracking
import re

class TestEnveloppes:

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.user_client = _client(user, "/enveloppes")
        self.admin_user_client = _client(admin_user, "/enveloppes")
        self.guess_client = _client(None, "/enveloppes")

    def test_add_affranchissement_valorisable(self, db: Session, enveloppe: Enveloppe) -> None:
        data = {
            "categorie": "MARI",
            "type": "VAL",
            "devise": "EURO",
            "prix_unite_devise": 1.26
        }

        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)
        assert response.status_code == 200

    from unittest.mock import patch
    from services.validateurs.types.validateur_s10 import ValidateurS10
    @patch.object(ValidateurS10, 'verification_ssu_tracking')
    def test_add_timbre_s10_to_enveloppe(self, mock_verification_ssu_tracking, db: Session, enveloppe: Enveloppe) -> None:
        data = {
            "code": S10().code,
        }

        # Send POST request to add a Timbre to the Enveloppe
        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)
        assert response.status_code == 200

        data = response.json()
        assert data["frontend_action"] == "RIEN"

        enveloppe = data["enveloppe"]        
        assert "id" in enveloppe
        assert "affranchissements" in enveloppe
        assert len(enveloppe["affranchissements"]) == 1
        assert len(db.exec(select(Affranchissement)).all()) == 1
        
        assert enveloppe["statut"] == "EDITION"
        assert data["affranchissement"]["type"] == "S10"
        
        aff = enveloppe["affranchissements"][0]
        
        #TODO: Réactiver les tests de validation
        # assert mock_verification_ssu_tracking.call_count == 1
        # assert aff["type"] == "S10"
        # assert aff["statut"] == "VALIDE"
        
        # aff = db.exec(select(Affranchissement)).first()
        # assert len(aff.verifications) == 5
        # expected_verifications = [
        #     ("SEQUENCE", "NON_DETERMINE"),
        #     ("GRAMMAIRE_CLE", "VALIDE"), 
        #     ("GRAMMAIRE_COUNTRY", "VALIDE"),
        #     ("COUNT", "VALIDE"),
        #     ("CODE_FRAUDULEUX", "VALIDE")            
        # ]

        # for i, (expected_type, expected_status) in enumerate(expected_verifications):
        #     assert aff.verifications[i].type == expected_type
        #     assert aff.verifications[i].statut == expected_status
        
    @patch_blob_operations()    
    def test_add_photo_success(self, enveloppe, db, user):
        """Test l'ajout réussi d'une photo à une enveloppe"""
        # Création d'un faux fichier image
        file_content = b"fake image content"
        file = UploadFile(
            filename="test.jpg",
            file=io.BytesIO(file_content),
        )
        
        # with patch('api.routes.enveloppes.edition.StorageService.upload_file', return_value="https://storage.example.com/test.jpg") as mock:
        response = self.user_client.post(
            f"/photos/token",
            files={"file": ("test.jpg", file_content, "image/jpeg")}
        )
        
        assert response.status_code == 200
        assert response.json()["id"] == enveloppe.id
        
        # Vérifie que la photo a été créée en base
        photos = db.exec(select(PhotoEnveloppe).where(PhotoEnveloppe.enveloppe_id == enveloppe.id)).all()
        assert len(photos) == 1
        assert photos[0].format == "image/jpeg"        
        assert "test" in photos[0].url
        assert ".jpg" in photos[0].url
        
    def test_add_photo_invalid_token(self):
        """Test l'ajout d'une photo avec un token invalide"""
        file_content = b"fake image content"
        
        response = self.guess_client.post(
            f"/photos/token",
            files={"file": ("test.jpg", file_content, "image/jpeg")}
        )
        
        assert response.status_code == 401
        assert response.json()["detail"] == "Not authenticated"

    @pytest.fixture
    def site(self, session):
        site = Site(nom="Site de test")
        session.add(site)
        session.commit()
        session.refresh(site)
        return site
    
    @pytest.fixture
    def destination(self, session):
        destination = Destination(nom_fr="France", alpha2="FR")
        session.add(destination)
        session.commit()
        session.refresh(destination)
        return destination
    
    @pytest.fixture
    def expediteur(self, session):
        expediteur = Expediteur(nom="Dupont", prenom="Jean")
        session.add(expediteur)
        session.commit()
        session.refresh(expediteur)
        return expediteur
    

    def test_create_enveloppe_success(self, site, destination, expediteur):
        """Test la création réussie d'une enveloppe avec tous les champs requis"""
        
        # Tente de créer une enveloppe, pas possible on doit demander une enveloppe en edition d'aboard
        data = {
            "poids": 100.0,
            "surpoids": False,
            "surdimensionne": False,
            "destination_enveloppe": "METROPOLE",
            "site_id": site.id,
            "destination_id": destination.id,
            "expediteur_id": expediteur.id
        }
        
        response = self.user_client.post("/", json=data)
        assert response.status_code == 404

        # Création d'une enveloppe en edition d'aboard
        response = self.user_client.get("/edition")
        assert response.status_code == 200

        # Ok on peut modifier l'enveloppe
        data["id"] = response.json()["id"]
        response = self.user_client.post("/", json=data)
        assert response.status_code == 200, response.json()

        created_enveloppe = response.json()
        assert created_enveloppe["site"]["id"] == site.id
        assert created_enveloppe["statut"] == StatutEnveloppeEnum.EDITION
        assert created_enveloppe["poids"] == 100.0

        # response = self.user_client.post("/", json={
        #     "poids": 100.0,
        #     "surpoids": False,
        #     "surdimensionne": False,
        #     "produit": "LV",
        #     "site_id": site.id
        # })
        # assert response.status_code == 200, "La création avec site_id devrait réussir"
    
    def test_create_enveloppe_failure(self, site, destination, expediteur):
        """Test la création réussie d'une enveloppe avec tous les champs requis"""
        data = {
            "poids": 100.0,
            "surpoids": False,
            "surdimensionne": False,
            "produit": "LV",
            "destination_id": destination.id,
            "expediteur_id": expediteur.id
        }
        
        response = self.user_client.post("/", json=data)
        assert response.status_code == 404

        data = {
            "id": 10,
            "poids": 100.0,
            "surpoids": False,
            "surdimensionne": False,
            "produit": "LV",
            "destination_id": destination.id,
            "expediteur_id": expediteur.id
        }
        
        response = self.user_client.post("/", json=data)
        assert response.status_code == 404
        
    def test_delete_affranchissement(self, db: Session, enveloppe: Enveloppe) -> None:
        """Test la suppression d'un affranchissement d'une enveloppe"""
        # D'abord, ajoutons un affranchissement à l'enveloppe
        data = {
            "categorie": "MARI",
            "type": "VAL",
            "devise": "EURO",
            "prix_unite_devise": 1.26
        }

        # Ajout de l'affranchissement
        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)
        assert response.status_code == 200
        
        # Récupération de l'ID de l'affranchissement créé
        affranchissement_id = response.json()["affranchissement"]["id"]
        
        # Vérification que l'affranchissement existe bien
        affranchissement = db.exec(select(Affranchissement).where(Affranchissement.id == affranchissement_id)).first()
        assert affranchissement is not None
        
        # Suppression de l'affranchissement
        response = self.user_client.delete(f"/{enveloppe.id}/affranchissements/{affranchissement_id}")
        assert response.status_code == 200
        
        # Vérification que l'affranchissement a bien été supprimé
        affranchissement = db.exec(select(Affranchissement).where(Affranchissement.id == affranchissement_id)).first()
        assert affranchissement is None
        
        # Vérification que l'enveloppe existe toujours
        enveloppe_db = db.exec(select(Enveloppe).where(Enveloppe.id == enveloppe.id)).first()
        assert enveloppe_db is not None
        assert enveloppe_db.updated_at is not None
        
        # Vérification de la réponse
        response_data = response.json()
        assert response_data["enveloppe"]["id"] == enveloppe.id
        assert response_data["affranchissement"] is None
        assert response_data["frontend_action"] == "RIEN"

    def test_delete_nonexistent_affranchissement(self, db: Session, enveloppe: Enveloppe) -> None:
        """Test la suppression d'un affranchissement qui n'existe pas"""
        # Tentative de suppression d'un affranchissement inexistant
        response = self.user_client.delete(f"/{enveloppe.id}/affranchissements/999")
        assert response.status_code == 404
        assert response.json()["detail"] == "Affranchissement non trouvé"
    
    @patch_blob_operations()
    def test_add_photo_authenticated(self, enveloppe, db):
        """Test l'ajout d'une photo à une enveloppe avec authentification"""
        # Création d'un faux fichier image
        file_content = b"fake image content"
        
        response = self.user_client.post(
            f"/{enveloppe.id}/photos",
            files={"file": ("photo_1.jpg", file_content, "image/jpeg")}
        )
        
        assert response.status_code == 200
        assert response.json()["id"] == enveloppe.id
        
        # Vérifie que la photo a été créée en base
        photos = db.exec(select(PhotoEnveloppe).where(PhotoEnveloppe.enveloppe_id == enveloppe.id)).all()
        assert len(photos) == 1
        assert photos[0].format == "image/jpeg"
        # assert photos[0].url == "https://storage.example.com/test.jpg"

        uuid_pattern = f'{enveloppe.uuid}/photo_1_[0-9a-f]{{8}}\.jpg'
        assert re.search(uuid_pattern, photos[0].url), f"L'URL {photos[0].url} ne contient pas le format attendu test_file_{{uuid}}.txt"


    def test_add_photo_unauthenticated(self, enveloppe):
        """Test l'ajout d'une photo sans authentification"""
        file_content = b"fake image content"
        
        response = self.guess_client.post(
            f"/{enveloppe.id}/photos",
            files={"file": ("test.jpg", file_content, "image/jpeg")}
        )
        
        assert response.status_code == 401

    def test_add_photo_invalid_file_type(self, enveloppe):
        """Test l'ajout d'un fichier qui n'est pas une image"""
        file_content = b"fake text content"
        
        response = self.user_client.post(
            f"/{enveloppe.id}/photos",
            files={"file": ("test.txt", file_content, "text/plain")}
        )
        
        assert response.status_code == 400
        assert response.json()["detail"] == "Le fichier doit être une image"

    @patch_blob_operations()
    def test_add_photo_terminated_enveloppe(self, db, enveloppe):
        """Test l'ajout d'une photo à une enveloppe terminée"""


        # Marquer l'enveloppe comme terminée
        enveloppe.statut = StatutEnveloppeEnum.TERMINEE
        db.add(enveloppe)
        db.commit()
        
        file_content = b"fake image content"
        
        response = self.user_client.post(
            f"/{enveloppe.id}/photos",
            files={"file": ("test.jpg", file_content, "image/jpeg")}
        )
        
        assert response.status_code == 200, response.json()

        photo = db.exec(select(PhotoEnveloppe)).first()
        assert "test" in photo.url
        
    def test_get_current_enveloppe_success(self, db: Session, enveloppe_complete: Enveloppe) -> None:
        """Test la récupération de l'enveloppe en cours d'édition"""
        # L'enveloppe de la fixture est déjà en statut EDITION
        response = self.user_client.get(f"/{enveloppe_complete.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == enveloppe_complete.id
        assert data["statut"] == "EDITION"
        assert "affranchissements" in data
        assert "photos" in data
        assert len(data["photos"]) == 3
        assert "site" in data
        assert "destination" in data
        assert "expediteur" in data
        assert "poids" in data
        assert "surpoids" in data
        assert "surdimensionne" in data
        
    def test_get_current_enveloppe_not_found(self, db: Session, enveloppe: Enveloppe) -> None:
        """Test la récupération quand il n'y a pas d'enveloppe en édition"""
        # Modification du statut de l'enveloppe pour qu'elle ne soit plus en édition
        enveloppe.statut = StatutEnveloppeEnum.TERMINEE
        db.add(enveloppe)
        db.commit()
        
        response = self.user_client.get("/-1")
        assert response.status_code == 404
        
    def test_delete_photo_success(self, db: Session, enveloppe: Enveloppe) -> None:
        """Test la suppression réussie d'une photo d'une enveloppe"""
        # D'abord, ajoutons une photo à l'enveloppe
        photo = PhotoEnveloppe(
            format="image/jpeg",
            enveloppe_id=enveloppe.id,
            url="https://storage.example.com/test.jpg"
        )
        db.add(photo)
        db.commit()
        db.refresh(photo)

        with patch('api.routes.enveloppes.edition.StorageService.delete_file') as mock_delete:
            response = self.user_client.delete(f"/{enveloppe.id}/photos/{photo.id}")
            
            assert response.status_code == 200
            assert response.json()["id"] == enveloppe.id
            
            # Vérifie que la photo a été supprimée en base
            photo_db = db.exec(select(PhotoEnveloppe).where(PhotoEnveloppe.id == photo.id)).first()
            assert photo_db is None
            
            # Vérifie que le storage service a été appelé correctement
            mock_delete.assert_called_once_with("https://storage.example.com/test.jpg")

    def test_delete_photo_not_found(self, db: Session, enveloppe: Enveloppe) -> None:
        """Test la suppression d'une photo qui n'existe pas"""
        response = self.user_client.delete(f"/{enveloppe.id}/photos/999")
        assert response.status_code == 404
        assert response.json()["detail"] == "Photo non trouvée"

    def test_delete_photo_terminated_enveloppe(self, db: Session, enveloppe: Enveloppe) -> None:
        """Test la suppression d'une photo d'une enveloppe terminée"""
        # Créer une photo
        photo = PhotoEnveloppe(
            format="image/jpeg",
            enveloppe_id=enveloppe.id,
            url="https://storage.example.com/test.jpg"
        )
        db.add(photo)
        
        # Marquer l'enveloppe comme terminée
        enveloppe.statut = StatutEnveloppeEnum.TERMINEE
        db.add(enveloppe)
        db.commit()
        
        with patch('api.routes.enveloppes.edition.StorageService.delete_file') as mock_delete:
            response = self.user_client.delete(f"/{enveloppe.id}/photos/{photo.id}")
            
            assert response.status_code == 200
            assert response.json()["id"] == enveloppe.id
            
            # Vérifie que la photo a été supprimée en base
            photo_db = db.exec(select(PhotoEnveloppe).where(PhotoEnveloppe.id == photo.id)).first()
            assert photo_db is None
            
            # Vérifie que le storage service a été appelé correctement
            mock_delete.assert_called_once_with("https://storage.example.com/test.jpg")
        
    @patch_ssu_tracking(finalized=False)
    def test_add_s10_then_update_statut_invalide(self, mock_verification_ssu_tracking, db: Session, enveloppe: Enveloppe) -> None:
        # Ajout initial de l'affranchissement S10
        data = {
            "code": S10().code,
        }
        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)
        assert response.status_code == 200

        aff_id = response.json()["affranchissement"]["id"]
        
        # Mise à jour du statut en invalide
        update_data = {
            "statut": "INVALIDE"
        }
        response = self.user_client.put(f"/{enveloppe.id}/affranchissements/{aff_id}", json=update_data)
        assert response.status_code == 200, response.json()
        
        # Vérification que le statut a bien été mis à jour
        data = response.json()
        assert data["affranchissement"]["statut"] == "INVALIDE"
        
        # Vérification en base de données
        aff = db.exec(select(Affranchissement).where(Affranchissement.id == aff_id)).first()
        assert aff.statut == "INVALIDE"
        
    def test_finaliser_enveloppe_complete(self, db: Session, enveloppe_complete: Enveloppe, user) -> None:
        """Test la finalisation d'une enveloppe complète avec photos et affranchissements"""
        # Vérifier que l'enveloppe est bien en statut EDITION avant la finalisation
        
        # Add Affranchissement
        aff = Affranchissement(
            categorie=CategorieAffranchissementEnum.MARI,
            type=TypeAffranchissementEnum.VAL,
            devise=DeviseEnum.EURO,
            prix_unite_devise=1.26,
            statut=StatutVerificationEnum.VALIDE,
            enveloppe_id=enveloppe_complete.id,
            user_id=user.id
        )
        db.add(aff)
        db.commit()
        db.refresh(aff)
        
        # Vérifier que l'enveloppe a bien des photos et des affranchissements
        assert len(enveloppe_complete.photos) == 3
        assert len(enveloppe_complete.affranchissements) > 0
        
        # Finaliser l'enveloppe
        response = self.user_client.post(f"/{enveloppe_complete.id}/terminer")
        assert response.status_code == 200, response.json()
        
        data = response.json()
        assert data["id"] == enveloppe_complete.id

        #REGLE_METIER: frauduleuse car sous affranchissement 
        assert data["statut"] == StatutEnveloppeEnum.SOUS_AFFRANCHI
        from services.enveloppe import ServiceEnveloppe
        assert ServiceEnveloppe(enveloppe_complete).affranchissements_invalides() == []
        assert ServiceEnveloppe(enveloppe_complete).calcul_cout_envoi() == 1.39
        assert ServiceEnveloppe(enveloppe_complete).prix_affranchissements_valide() == 1.26

        assert data["valorisation"] is not None
        assert data["valorisation"]["postage"] is not None
        assert data["valorisation"]["livraison"] is not None

    def test_get_enveloppe_edition(self, db: Session, site: Site, user):
        # Créer une enveloppe avec le statut TERMINE
        enveloppe = Enveloppe(
            statut=StatutEnveloppeEnum.TERMINEE,
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            poids=100,
            surpoids=False,
            surdimensionne=False,
            site=site,
            user=user
        )
        db.add(enveloppe)
        db.commit()

        assert len(db.exec(select(Enveloppe)).all()) == 1

        # Appeler la route GET /enveloppes/edition
        response = self.user_client.get("/edition")
        
        # Vérifier que la réponse est correcte
        assert response.status_code == 200, response.json()
        assert len(db.exec(select(Enveloppe)).all()) == 2

    def test_admin_or_manager_can_edit_enveloppe(self, db: Session, user, admin_user, manager_user, site: Site):
        """Test que les admins et les managers peuvent modifier une enveloppe"""
        # Créer une enveloppe avec le statut TERMINE
        enveloppe = Enveloppe(
            statut=StatutEnveloppeEnum.TERMINEE,
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            poids=100,
            surpoids=False,
            surdimensionne=False,
            site=site,
            user=user
        )
        db.add(enveloppe)
        db.commit()

        # Vérifier que l'enveloppe est bien dans la base de données
        assert len(db.exec(select(Enveloppe)).all()) == 1

        user_2 = create_user(session=db, user_create=User(
            email="<EMAIL>",
            hashed_password=get_password_hash("thisisstrong"),
            site_id=site.id,
            role=UserRole.USER
        ))

        users_responses = [
            (admin_user, 200),
            (manager_user, 200),
            (user_2, 404)
        ]

        for _user, _status_code in users_responses:
            
            client = _client(_user, "/enveloppes/")

            # Vérifier que l'enveloppe est bien modifiable par l'admin
            response = client.post(f"/{enveloppe.id}/add", 
                                              json={"categorie": "MARI", "type": "VAL", "devise": "EURO", "prix_unite_devise": 1.26})
            assert response.status_code == _status_code, response.json()

            if response.status_code == 200:
                id = response.json()["affranchissement"]["id"]

                # On peut modifier un affranchissement
                response = client.put(f"/{enveloppe.id}/affranchissements/{id}", 
                                    json={"statut": "INVALIDE"})
                assert response.status_code == _status_code, response.json()

                # On peut supprimer un affranchissement
                response = client.delete(f"/{enveloppe.id}/affranchissements/{id}")
                assert response.status_code == _status_code, response.json()

    def test_user_roissy_default_product(self, db: Session):
        """Test que le produit par défaut est LV pour les utilisateurs de Roissy"""
        # Créer un utilisateur de Roissy
        site_roissy = Site(
            nom="Roissy",
        )
        db.add(site_roissy)
        db.commit()

        user = create_user(session=db, user_create=User(
            email="<EMAIL>",
            hashed_password=get_password_hash("thisisstrong"),
            site_id=site_roissy.id,
            role=UserRole.USER
        ))

        http_client = _client(user, "/enveloppes/")

        # Créer une enveloppe
        response = http_client.get("/edition")
        assert response.status_code == 200
        assert response.json()["destination_enveloppe"] == "INTERNATIONAL"

    @patch_blob_operations()
    def test_delete_enveloppe_success(self, db: Session, enveloppe: Enveloppe) -> None:
        """Test la suppression réussie d'une enveloppe"""
        # Ajouter une photo à l'enveloppe
        photo = PhotoEnveloppe(
            format="image/jpeg",
            enveloppe_id=enveloppe.id,
            url="https://storage.example.com/test.jpg"
        )
        db.add(photo)
        
        # Ajouter un affranchissement à l'enveloppe
        data = {
            "categorie": "MARI",
            "type": "VAL",
            "devise": "EURO",
            "prix_unite_devise": 1.26
        }
        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)
        assert response.status_code == 200
        
        # Vérifier que l'enveloppe existe avec ses relations
        assert len(db.exec(select(Enveloppe)).all()) == 1
        assert len(db.exec(select(PhotoEnveloppe)).all()) == 1
        assert len(db.exec(select(Affranchissement)).all()) == 1
        
        with patch('api.routes.enveloppes.edition.StorageService.delete_file') as mock_delete:
            # Supprimer l'enveloppe
            response = self.user_client.delete(f"/{enveloppe.id}")
            
            # Vérifier la réponse
            assert response.status_code == 200
            assert response.json()["id"] == enveloppe.id
            
            # Vérifier que l'enveloppe et ses relations ont été supprimées
            assert len(db.exec(select(Enveloppe)).all()) == 0
            assert len(db.exec(select(PhotoEnveloppe)).all()) == 0
            assert len(db.exec(select(Affranchissement)).all()) == 0
            
            # Vérifier que le service de stockage a été appelé pour supprimer la photo
            mock_delete.assert_called_once_with("https://storage.example.com/test.jpg")

    def test_delete_enveloppe_unauthorized(self, db: Session, enveloppe: Enveloppe) -> None:
        """Test la suppression d'une enveloppe par un utilisateur non autorisé"""
        # Tentative de suppression sans authentification
        response = self.guess_client.delete(f"/{enveloppe.id}")
        assert response.status_code == 401
        
        # Vérifier que l'enveloppe existe toujours
        assert len(db.exec(select(Enveloppe)).all()) == 1

    def test_delete_enveloppe_not_found(self) -> None:
        """Test la suppression d'une enveloppe qui n'existe pas"""
        response = self.user_client.delete("/999")
        assert response.status_code == 404

    def test_delete_enveloppe_forbidden(self, db: Session, enveloppe: Enveloppe, site: Site) -> None:
        """Test la suppression d'une enveloppe par un utilisateur qui n'en est pas le propriétaire"""
        # Créer un autre utilisateur
        autre_user = create_user(session=db, user_create=User(
            email="<EMAIL>",
            hashed_password=get_password_hash("motdepasse"),
            site_id=site.id,
            role=UserRole.USER
        ))
        
        # Créer un client pour cet utilisateur
        autre_user_client = _client(autre_user, "/enveloppes")
        
        # Tentative de suppression par un utilisateur qui n'est pas le propriétaire
        response = autre_user_client.delete(f"/{enveloppe.id}")
        
        # Vérifier que la réponse est un refus (404 Not Found)
        assert response.status_code == 404

    def test_modify_enveloppe_surpoids(self, db: Session, enveloppe: Enveloppe) -> None:
        """Test la modification du poids d'une enveloppe pour la marquer en surpoids"""
        # Modifier le poids de l'enveloppe à 2001g
        data = {
            "id": enveloppe.id,
            "poids": 2001
        }
        response = self.user_client.post(f"/", json=data)
        
        # Vérifier que la modification a réussi
        assert response.status_code == 200, response.json()
        
        # Vérifier que l'enveloppe est maintenant en surpoids
        updated_enveloppe = response.json()
        assert updated_enveloppe["surpoids"] == True
        
        # Vérifier en base de données
        db.refresh(enveloppe)
        assert enveloppe.poids == 2001
        assert enveloppe.surpoids == True


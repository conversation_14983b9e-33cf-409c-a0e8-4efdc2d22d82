from tests.conftest import client as _client
import pytest
from sqlmodel import Session, select
from models.business import Enveloppe, Affranchissement, PhotoEnveloppe, Site, Destination, Expediteur
from constants.enumerations import CategorieAffranchissementEnum, DeviseEnum, ProduitEnum, StatutEnveloppeEnum, TypeAffranchissementEnum, DestinationEnveloppeEnum
from tests.fixtures.users import create_user, get_password_hash
from models.users import UserRole, User


class TestEnveloppes:

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.user_client = _client(user, "/enveloppes")
        self.admin_user_client = _client(admin_user, "/enveloppes")
        self.guess_client = _client(None, "/enveloppes")
            
    def test_get_enveloppes_filter_by_user_email(self, db: Session, user, admin_user, site: Site):
        """Test la récupération des enveloppes en filtrant par email de l'utilisateur"""
        
        # Créer une enveloppe pour l'utilisateur
        enveloppe = Enveloppe(
            statut=StatutEnveloppeEnum.TERMINEE,
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            poids=100,
            surpoids=False,
            surdimensionne=False,
            site=site,
            user=user
        )
        db.add(enveloppe)
        db.commit()

        # Vérifier que l'enveloppe est bien dans la base de données
        assert len(db.exec(select(Enveloppe)).unique().all()) == 1

        # Utiliser l'email de l'utilisateur pour filtrer les enveloppes
        response = self.admin_user_client.get("/", params={"user_email": user.email})
        
        # Vérifier que la réponse est correcte
        assert response.status_code == 200, response.json()
        data = response.json()
        assert len(data["items"]) == 1
        assert data["items"][0]["id"] == enveloppe.id
        assert data["items"][0]["user"]["email"] == user.email

        # Utiliser l'email de l'utilisateur pour filtrer les enveloppes
        response = self.admin_user_client.get("/", params={"user_email": "lucasv"})
        
        # Vérifier que la réponse est correcte
        assert response.status_code == 200, response.json()
        data = response.json()
        assert len(data["items"]) == 0

    def test_get_enveloppes_search_global(self, db: Session, user, admin_user, site: Site):
        """Test la recherche globale des enveloppes par email, expéditeur ou code d'affranchissement"""
        
        # Créer un expéditeur
        expediteur = Expediteur(
            nom="Dupont Entreprise 123",
            adresse="123 Rue Test",
            ville="Paris",
            code_postal="75001"
        )
        db.add(expediteur)
        db.commit()
        
        # Créer une enveloppe pour l'utilisateur avec l'expéditeur
        enveloppe1 = Enveloppe(
            statut=StatutEnveloppeEnum.TERMINEE,
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            poids=100,
            surpoids=False,
            surdimensionne=False,
            site=site,
            user=user,
            expediteur=expediteur
        )
        db.add(enveloppe1)
        
        # Créer une autre enveloppe sans expéditeur
        enveloppe2 = Enveloppe(
            statut=StatutEnveloppeEnum.TERMINEE,
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            poids=150,
            surpoids=False,
            surdimensionne=False,
            site=site,
            user=admin_user
        )
        db.add(enveloppe2)
        
        # Ajouter un affranchissement à l'enveloppe2
        affranchissement = Affranchissement(
            code="CODE123456",
            categorie=CategorieAffranchissementEnum.CODE,
            type=TypeAffranchissementEnum.S10,
            enveloppe=enveloppe2,
            user=admin_user
        )
        db.add(affranchissement)
        db.commit()
        
        # Test recherche par email utilisateur
        response = self.admin_user_client.get("/", params={"recherche": user.email[:5]})
        assert response.status_code == 200, response.json()
        data = response.json()
        assert len(data["items"]) == 1
        assert data["items"][0]["id"] == enveloppe1.id
        
        # Test recherche par nom d'expéditeur
        response = self.admin_user_client.get("/", params={"recherche": "Dupont"})
        assert response.status_code == 200, response.json()
        data = response.json()
        assert len(data["items"]) == 1
        assert data["items"][0]["id"] == enveloppe1.id
        
        # Test recherche par code d'affranchissement
        response = self.admin_user_client.get("/", params={"recherche": "CODE123"})
        assert response.status_code == 200, response.json()
        data = response.json()
        assert len(data["items"]) == 1
        assert data["items"][0]["id"] == enveloppe2.id
        
        # Test recherche sans résultat
        response = self.admin_user_client.get("/", params={"recherche": "inexistant"})
        assert response.status_code == 200, response.json()
        data = response.json()
        assert len(data["items"]) == 0

        # Test recherche par 123 : trouve dupont et le code
        response = self.admin_user_client.get("/", params={"recherche": "123"})
        assert response.status_code == 200, response.json()
        data = response.json()
        assert len(data["items"]) == 2


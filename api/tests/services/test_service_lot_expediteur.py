import pytest
from datetime import datetime
from unittest.mock import MagicMock, patch
from sqlmodel import Session
from fastapi import HTTPException
from models.business import LotExpediteur, Site, PaiementLotExpediteur, C<PERSON>er, Enveloppe, Expediteur
from models.users import User
from services.lot_expediteur import ServiceLotExpediteur
from constants.enumerations import StatutLotExpediteurEnum, OptionTraitementLotExpediteurEnum, DestinationEnveloppeEnum, StatutEnveloppeEnum

class TestServiceLotExpediteur:
    
    @pytest.fixture
    def mock_session(self):
        return MagicMock()
    
    @pytest.fixture
    def service(self, mock_session):
        return ServiceLotExpediteur(mock_session)
    
    @pytest.fixture
    def site_libourne(self):
        site = Site(id=1, nom="LIBOURNE")
        return site
    
    @pytest.fixture
    def site_roissy(self):
        site = Site(id=2, nom="ROISSY")
        return site
    
    @pytest.fixture
    def user_libourne(self, site_libourne):
        user = User(id=1, username="user_libourne", site=site_libourne)
        return user
    
    @pytest.fixture
    def user_roissy(self, site_roissy):
        user = User(id=2, username="user_roissy", site=site_roissy)
        return user
    
    def test_changer_statut_lot_transition_autorisee_libourne(self, service, user_libourne):
        """Test le changement de statut avec une transition autorisée pour un utilisateur de Libourne"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=1, statut=StatutLotExpediteurEnum.FERME)
        
        # Appeler la méthode avec un nouveau statut autorisé pour Libourne
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.NOTIFIE, 
            user=user_libourne
        )
        
        # Vérifier que le statut a été changé
        assert result is not None
        assert result.statut == StatutLotExpediteurEnum.NOTIFIE
        assert result.user_modification_id == user_libourne.id
        assert result.updated_at is not None
        
        # Vérifier que la session a été commit 1x ou plus
        service.session.commit.assert_called()
    
    def test_changer_statut_lot_transition_autorisee_roissy(self, service, user_roissy):
        """Test le changement de statut avec une transition autorisée pour un utilisateur de Roissy"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=2, statut=StatutLotExpediteurEnum.TRAITE_LIBERABLE)
        
        # Appeler la méthode avec un nouveau statut autorisé pour Roissy
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.TRAITE_OPERATIONNEL, 
            user=user_roissy
        )
        
        # Vérifier que le statut a été changé
        assert result is not None
        assert result.statut == StatutLotExpediteurEnum.TRAITE_OPERATIONNEL
        assert result.user_modification_id == user_roissy.id
        assert result.updated_at is not None
        
    
    def test_changer_statut_lot_transition_non_autorisee(self, service, user_roissy):
        """Test le changement de statut avec une transition non autorisée"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=3, statut=StatutLotExpediteurEnum.FERME)
        
        # Appeler la méthode avec un nouveau statut non autorisé pour Roissy
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.NOTIFIE, 
            user=user_roissy
        )
        
        # Vérifier que le résultat est None (transition non autorisée)
        assert result is None
        
        # Vérifier que le statut n'a pas été changé
        assert lot.statut == StatutLotExpediteurEnum.FERME
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    def test_changer_statut_lot_meme_statut(self, service, user_libourne):
        """Test le changement de statut avec le même statut"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=4, statut=StatutLotExpediteurEnum.FERME)
        
        # Appeler la méthode avec le même statut
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.FERME, 
            user=user_libourne
        )
        
        # Vérifier que le lot est retourné sans modification
        assert result is lot
        assert result.statut == StatutLotExpediteurEnum.FERME
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    def test_changer_statut_lot_transition_inexistante(self, service, user_libourne):
        """Test le changement de statut avec une transition qui n'existe pas dans la matrice"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=5, statut=StatutLotExpediteurEnum.OUVERT)
        
        # Appeler la méthode avec un nouveau statut qui n'est pas dans la matrice
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.TRAITE_OPERATIONNEL, 
            user=user_libourne
        )
        
        # Vérifier que le résultat est None (transition non autorisée)
        assert result is None
        
        # Vérifier que le statut n'a pas été changé
        assert lot.statut == StatutLotExpediteurEnum.OUVERT
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    def test_changer_statut_lot_liberation_casiers(self, service, user_roissy):
        """Test le changement de statut avec libération des casiers"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=6, statut=StatutLotExpediteurEnum.TRAITE_LIBERABLE)
        
        # Mock de la méthode liberer_casiers
        service.liberer_casiers = MagicMock()
        
        # Appeler la méthode avec un statut qui déclenche la libération des casiers
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.TRAITE_OPERATIONNEL, 
            user=user_roissy
        )
        
        # Vérifier que le statut a été changé
        assert result is not None
        assert result.statut == StatutLotExpediteurEnum.TRAITE_OPERATIONNEL
        
        # Vérifier que la méthode liberer_casiers a été appelée
        service.liberer_casiers.assert_called_once_with(lot)
        
        # Vérifier que la session a été commit
        service.session.commit.assert_called_once()
    
    def test_changer_statut_lot_none(self, service, user_libourne):
        """Test le changement de statut avec un lot None"""
        # Appeler la méthode avec un lot None
        result = service.changer_statut_lot_avec_verification(
            lot=None, 
            nouveau_statut=StatutLotExpediteurEnum.FERME, 
            user=user_libourne
        )
        
        # Vérifier que le résultat est None
        assert result is None
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    @pytest.mark.parametrize("option,valorisation,expected", [
        (
            OptionTraitementLotExpediteurEnum.LIVRAISON_DESTINATAIRES,
            {"livraison": {"cout_total": 42}},
            42
        ),
        (
            OptionTraitementLotExpediteurEnum.RECUPERATION_SUR_SITE,
            {"collecte": {"cout_ttc": 15.5}},
            15.5
        ),
        (
            OptionTraitementLotExpediteurEnum.RENVOI_EXPEDITEUR,
            {"expédition": {"cout_ttc": 99.99}},
            99.99
        ),
    ])
    def test_creer_paiements_selon_option(self, mock_session, option, valorisation, expected):
        lot = LotExpediteur(
            id=123,
            paiements=[],
            option_recouvrement=option,
            montant_ttc=None
        )

        # Patch la méthode de valorisation pour retourner notre dict de test
        with patch.object(ServiceLotExpediteur, "calcul_somme_valorisation", return_value=valorisation):
            service = ServiceLotExpediteur(mock_session)
            lot.paiements = []

            result = service.creer_paiements(lot)

            # Vérifie que le montant TTC du lot est correct
            assert result.montant_ttc == expected

            # Vérifie qu'un paiement a été ajouté à la session avec le bon montant
            paiement = None
            for call in mock_session.add.call_args_list:
                arg = call[0][0]
                if isinstance(arg, PaiementLotExpediteur):
                    paiement = arg
                    break
            assert paiement is not None
            assert paiement.montant_ttc == expected
            assert paiement.pourcentage == 100
            
            # Vérifie que la session a bien commit et refresh
            mock_session.commit.assert_called_once()
            mock_session.refresh.assert_called_once_with(lot)

    @pytest.fixture
    def site_source(self, db):
        site = Site(nom="Site Source")
        db.add(site)
        db.commit()
        db.refresh(site)
        return site

    @pytest.fixture
    def site_destination(self, db):
        site = Site(nom="Site Destination")
        db.add(site)
        db.commit()
        db.refresh(site)
        return site

    @pytest.fixture
    def expediteur_test(self, db):
        expediteur = Expediteur(
            nom="Test Expediteur",
            adresse="123 Rue Test",
            ville="Paris",
            code_postal="75001"
        )
        db.add(expediteur)
        db.commit()
        db.refresh(expediteur)
        return expediteur

    @pytest.fixture
    def user_test(self, db, site_source):
        user = User(
            email="<EMAIL>",
            hashed_password="hashed",
            site_id=site_source.id
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def test_deplacer_plis_lot_vers_casier_succes(self, db, site_source, site_destination, expediteur_test, user_test):
        """Test le déplacement réussi des plis d'un lot vers un casier"""
        # Créer les casiers
        casier_source1 = Casier(numero="C001", site_id=site_source.id)
        casier_source2 = Casier(numero="C002", site_id=site_source.id)
        casier_destination = Casier(numero="C003", site_id=site_destination.id)

        db.add_all([casier_source1, casier_source2, casier_destination])
        db.commit()
        db.refresh(casier_source1)
        db.refresh(casier_source2)
        db.refresh(casier_destination)

        # Créer le lot
        lot = LotExpediteur(
            expediteur_id=expediteur_test.id,
            statut=StatutLotExpediteurEnum.OUVERT,
            site_id=site_source.id,
            casier_id=casier_source1.id
        )
        db.add(lot)
        db.commit()
        db.refresh(lot)

        # Associer les casiers au lot
        casier_source1.lot_expediteur_id = lot.id
        casier_source2.lot_expediteur_id = lot.id
        db.commit()

        # Créer les enveloppes dans différents casiers
        enveloppe1 = Enveloppe(
            casier_id=casier_source1.id,
            site_id=site_source.id,
            lot_expediteur_id=lot.id,
            user_id=user_test.id,
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            statut=StatutEnveloppeEnum.FRAUDULEUSE
        )
        enveloppe2 = Enveloppe(
            casier_id=casier_source1.id,
            site_id=site_source.id,
            lot_expediteur_id=lot.id,
            user_id=user_test.id,
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            statut=StatutEnveloppeEnum.FRAUDULEUSE
        )
        enveloppe3 = Enveloppe(
            casier_id=casier_source2.id,
            site_id=site_source.id,
            lot_expediteur_id=lot.id,
            user_id=user_test.id,
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            statut=StatutEnveloppeEnum.FRAUDULEUSE
        )
        db.add_all([enveloppe1, enveloppe2, enveloppe3])
        db.commit()

        # Exécuter la méthode
        service = ServiceLotExpediteur(db)
        result = service.deplacer_plis_lot_vers_casier(lot_id=lot.id, casier_destination_id=casier_destination.id)

        # Vérifications du résultat
        assert result["nb_plis_deplaces"] == 3  # 3 enveloppes dans des casiers
        assert result["lot_expediteur"]["id"] == lot.id
        assert result["casier_destination"]["id"] == casier_destination.id
        assert result["casier_destination"]["numero"] == "C003"
        assert result["nb_casiers_sources_liberes"] == 2

        # Rafraîchir les objets depuis la base
        db.refresh(enveloppe1)
        db.refresh(enveloppe2)
        db.refresh(enveloppe3)
        db.refresh(casier_destination)
        db.refresh(casier_source1)
        db.refresh(casier_source2)
        db.refresh(lot)

        # Vérifier que les enveloppes ont été déplacées
        assert enveloppe1.casier_id == casier_destination.id
        assert enveloppe2.casier_id == casier_destination.id
        assert enveloppe3.casier_id == casier_destination.id

        # Vérifier que le casier de destination est associé au lot
        assert casier_destination.lot_expediteur_id == lot.id
        assert casier_destination.date_attribution is not None

        # Vérifier que les anciens casiers ont été libérés
        assert casier_source1.lot_expediteur_id is None
        assert casier_source2.lot_expediteur_id is None
        assert casier_source1.date_liberation is not None
        assert casier_source2.date_liberation is not None

        # Vérifier que le casier principal du lot a été mis à jour
        assert lot.casier_id == casier_destination.id

    def test_deplacer_plis_lot_vers_casier_lot_inexistant(self, db):
        """Test avec un lot inexistant"""
        service = ServiceLotExpediteur(db)

        with pytest.raises(HTTPException) as exc_info:
            service.deplacer_plis_lot_vers_casier(lot_id=999, casier_destination_id=1)

        assert exc_info.value.status_code == 404
        assert "Lot expéditeur non trouvé" in str(exc_info.value.detail)

    def test_deplacer_plis_lot_vers_casier_casier_inexistant(self, db, site_source, expediteur_test):
        """Test avec un casier de destination inexistant"""
        # Créer un lot
        lot = LotExpediteur(
            expediteur_id=expediteur_test.id,
            statut=StatutLotExpediteurEnum.OUVERT,
            site_id=site_source.id
        )
        db.add(lot)
        db.commit()

        service = ServiceLotExpediteur(db)

        with pytest.raises(HTTPException) as exc_info:
            service.deplacer_plis_lot_vers_casier(lot_id=lot.id, casier_destination_id=999)

        assert exc_info.value.status_code == 404
        assert "Casier de destination non trouvé" in str(exc_info.value.detail)

    def test_deplacer_plis_lot_vers_casier_casier_occupe(self, db, site_source, site_destination, expediteur_test):
        """Test avec un casier de destination déjà occupé par un autre lot"""
        # Créer deux lots
        lot1 = LotExpediteur(
            expediteur_id=expediteur_test.id,
            statut=StatutLotExpediteurEnum.OUVERT,
            site_id=site_source.id
        )
        lot2 = LotExpediteur(
            expediteur_id=expediteur_test.id,
            statut=StatutLotExpediteurEnum.OUVERT,
            site_id=site_destination.id
        )
        db.add_all([lot1, lot2])
        db.commit()

        # Créer un casier occupé par lot2
        casier_destination = Casier(
            numero="C003",
            site_id=site_destination.id,
            lot_expediteur_id=lot2.id
        )
        db.add(casier_destination)
        db.commit()

        service = ServiceLotExpediteur(db)

        with pytest.raises(HTTPException) as exc_info:
            service.deplacer_plis_lot_vers_casier(lot_id=lot1.id, casier_destination_id=casier_destination.id)

        assert exc_info.value.status_code == 400
        assert "déjà associé à un autre lot" in str(exc_info.value.detail)

    def test_deplacer_plis_lot_vers_casier_aucun_pli(self, db, site_source, site_destination, expediteur_test, user_test):
        """Test avec un lot sans plis dans des casiers"""
        # Créer le lot
        lot = LotExpediteur(
            expediteur_id=expediteur_test.id,
            statut=StatutLotExpediteurEnum.OUVERT,
            site_id=site_source.id
        )
        db.add(lot)
        db.commit()

        # Créer une enveloppe sans casier
        enveloppe_sans_casier = Enveloppe(
            casier_id=None,
            site_id=site_source.id,
            lot_expediteur_id=lot.id,
            user_id=user_test.id,
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            statut=StatutEnveloppeEnum.FRAUDULEUSE
        )
        db.add(enveloppe_sans_casier)
        db.commit()

        # Créer un casier de destination
        casier_destination = Casier(numero="C003", site_id=site_destination.id)
        db.add(casier_destination)
        db.commit()

        service = ServiceLotExpediteur(db)

        with pytest.raises(HTTPException) as exc_info:
            service.deplacer_plis_lot_vers_casier(lot_id=lot.id, casier_destination_id=casier_destination.id)

        assert exc_info.value.status_code == 400
        assert "Aucun pli trouvé dans les casiers du lot" in str(exc_info.value.detail)
import pytest
from constants.enumerations import TypeVerificationAffranchissementEnum, StatutVerificationEnum, TypeRegleMetierEnum, TypeAffranchissementEnum
from tests.conftest import client as _client
from sqlmodel import Session, select, select
from tests.fixtures.affranchissements import S10
from services.validateurs.types.validateur_s10 import ValidateurS10
from models.business import RegleMetier

class TestValidateurS10:

    @pytest.fixture(autouse=True)
    def setup(self, db):
        self.validateur = ValidateurS10(S10())

    def reset(self, db):
        # Delete all existing S10 RegleMetier
        regles_to_delete = db.exec(select(RegleMetier).where(RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10, RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE)).all()
        for regle in regles_to_delete:
            db.delete(regle)
        regles_expiration_to_delete = db.exec(select(RegleMetier).where(RegleMetier.cle == "S10_SEQUENCE_EXPIRATION_ANNEES")).all()
        for regle in regles_expiration_to_delete:
            db.delete(regle)
        db.commit()

    def test_verification_grammaire_invalid(self):
        # Créez un objet S10validateur et testez la méthode verification_grammaire
        self.validateur.verification_grammaire()
        
        # # Vérifiez les résultats attendus
        # verification_service = self.validateur.affranchissement.verifications[0]
        # assert verification_service.type == TypeVerificationAffranchissementEnum.GRAMMAIRE_SERVICE
        # assert verification_service.statut == StatutVerificationEnum.INVALIDE
        # assert "Service LV non autorisé. Liste des services non-autorisés: " in verification_service.message

        verification_key = self.validateur.affranchissement.verifications[0]
        assert verification_key.type == TypeVerificationAffranchissementEnum.GRAMMAIRE_CLE
        assert verification_key.statut == StatutVerificationEnum.VALIDE
        assert verification_key.message == "Clé 1 égal à 1"

        verification_country = self.validateur.affranchissement.verifications[1]
        assert verification_country.type == TypeVerificationAffranchissementEnum.GRAMMAIRE_COUNTRY
        assert verification_country.statut == StatutVerificationEnum.VALIDE
        assert verification_country.message == "Pays FR égal à FR"

        

    def test_verification_grammaire_valid(self):
        self.validateur.verification_grammaire()

        # assert self.validateur.affranchissement.verifications[0].type == TypeVerificationAffranchissementEnum.GRAMMAIRE_SERVICE
        # assert self.validateur.affranchissement.verifications[0].statut == StatutVerificationEnum.VALIDE
        # assert "Service LL autorisé. Liste des services non-autorisés: " in self.validateur.affranchissement.verifications[0].message
    
    def test_verification_sequences_valid(self, db):
        self.reset(db)

        #LL014699171FR
        db.add(RegleMetier(cle="S10_LA", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469917", "fin": "01469918", "service": "LL", "date_attribution": "01/06/2025"}))
        db.commit()
        

        self.validateur.verification_sequences()

        assert self.validateur.affranchissement.verifications[0].type == TypeVerificationAffranchissementEnum.SEQUENCE
        assert self.validateur.affranchissement.verifications[0].statut == StatutVerificationEnum.VALIDE

    def test_verification_sequences_invalid(self, db):
        self.reset(db)

        #LL014699171FR
        db.add(RegleMetier(cle="S10_LA", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469918", "fin": "01469919", "service": "LL"}))
        db.commit()
        

        self.validateur.verification_sequences()

        assert self.validateur.affranchissement.verifications[0].type == TypeVerificationAffranchissementEnum.SEQUENCE
        assert self.validateur.affranchissement.verifications[0].statut == StatutVerificationEnum.INVALIDE
        # assert "non reconnue dans les plages autorisées" in self.validateur.affranchissement.verifications[0].message
        
    def test_verification_sequences_invalid_expired_with_offset(self, db):
        self.reset(db)

        #LL014699171FR
        db.add(RegleMetier(cle="S10_SEQUENCE_EXPIRATION_ANNEES", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.VALEUR, 
                           valeur={"valeur": 2}))
        
        db.add(RegleMetier(cle="S10_LA", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469910", "fin": "01469919", 
                                   "service": "LL", 
                                   "date_attribution": "01/10/2020"},
                                   active=False))
        db.commit()
        self.validateur.verification_sequences()

        assert self.validateur.affranchissement.verifications[0].type == TypeVerificationAffranchissementEnum.SEQUENCE
        assert self.validateur.affranchissement.verifications[0].statut == StatutVerificationEnum.INVALIDE
        # assert "ancienne" in self.validateur.affranchissement.verifications[0].message
        # assert "(> 2 ans)" in self.validateur.affranchissement.verifications[0].message
        
    def test_verification_sequences_invalid_expired(self, db):
        self.reset(db)

        #LL014699171FR
        db.add(RegleMetier(cle="S10_LA", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469910", "fin": "01469919", 
                                   "service": "LL", 
                                   "date_attribution": "01/10/2020"},
                            active=False))
        db.commit()
        self.validateur.verification_sequences()

        assert self.validateur.affranchissement.verifications[0].type == TypeVerificationAffranchissementEnum.SEQUENCE
        assert self.validateur.affranchissement.verifications[0].statut == StatutVerificationEnum.INVALIDE
        # assert "ancienne" in self.validateur.affranchissement.verifications[0].message
        # assert "(> 3 ans)" in self.validateur.affranchissement.verifications[0].message

    def test_verification_sequences_avec_vraies_sequences(self, db):
        """
        On test avec les vraies séquences en base de PROD (via le dumps)
        """

        tests = [
            ("LL014699171FR", True),
            ("LL014699181FR", True),
            ("LL014699191FR", True),
        ]

        for code, attendu in tests:
            affranchissement = S10(code=code)
            validateur = ValidateurS10(affranchissement)
            validateur.verification_sequences()

            assert validateur.affranchissement.verifications[0].statut == (StatutVerificationEnum.VALIDE if attendu else StatutVerificationEnum.INVALIDE), f"Problème avec le code {code} doit être {'VALIDE' if attendu else 'INVALIDE'}"

    def test_verification_sequences_trop_vieille(self, db):
        self.reset(db)

        # 2 séquences, une vielle qui correspond et une récente
        # erreur car la séquence correspondante n'est pas la derniere

        # Ajouter une séquence trop vieille (plus de 5 ans)
        db.add(RegleMetier(cle="S10_SEQUENCE_EXPIRATION_ANNEES", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.VALEUR, 
                           valeur={"valeur": 2}))
        
        # LL014699171FR
        db.add(RegleMetier(cle="S10_LL_VIEILLE", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469917", "fin": "01469918", 
                                   "service": "LL", 
                                   "date_attribution": "01/01/2018"},
                           active=True))
        db.add(RegleMetier(cle="S10_LL_RECENTE", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469920", "fin": "01469921", 
                                   "service": "LL", 
                                   "date_attribution": "01/01/2022"},
                           active=True))
        db.commit()
        
        self.validateur.verification_sequences()

        assert self.validateur.affranchissement.verifications[0].type == TypeVerificationAffranchissementEnum.SEQUENCE
        assert self.validateur.affranchissement.verifications[0].statut == StatutVerificationEnum.INVALIDE
        assert "trop ancienne" in self.validateur.affranchissement.verifications[0].message

    def test_verification_sequences_inexistante(self, db):
        self.reset(db)
        
        # Aucune séquence correspondante en base
        self.validateur.verification_sequences()

        assert self.validateur.affranchissement.verifications[0].type == TypeVerificationAffranchissementEnum.SEQUENCE
        assert self.validateur.affranchissement.verifications[0].statut == StatutVerificationEnum.INVALIDE
        assert "Aucune séquence trouvée" in self.validateur.affranchissement.verifications[0].message

    def test_verification_sequences_vieille_mais_derniere(self, db):
        self.reset(db)

        # Ajouter une règle pour la durée de validité
        db.add(RegleMetier(cle="S10_SEQUENCE_EXPIRATION_ANNEES", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.VALEUR, 
                           valeur={"valeur": 2}))
        
        # Ajouter une séquence vieille mais qui est la dernière pour ce service
        db.add(RegleMetier(cle="S10_LL_DERNIERE", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469917", "fin": "01469918", 
                                   "service": "LL", 
                                   "date_attribution": "01/01/2018"},
                           active=True))
                           
        db.commit()
        
        # S'assurer qu'il n'y a pas d'autre séquence plus récente pour ce service
        from datetime import datetime
        from sqlmodel import func, JSON, cast
        from core.db import get_session
        
        self.validateur.verification_sequences()

        assert self.validateur.affranchissement.verifications[0].type == TypeVerificationAffranchissementEnum.SEQUENCE
        assert self.validateur.affranchissement.verifications[0].statut == StatutVerificationEnum.VALIDE
        assert "la plus récente" in self.validateur.affranchissement.verifications[0].message

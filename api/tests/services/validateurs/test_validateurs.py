import pytest
from sqlmodel import Session, select
from services.affranchissement import ServiceAffranchissement
from services.validateurs.validateur_affranchissement import ValidateurAffranchissement
from constants.enumerations import TypeAffranchissementEnum, StatutVerificationEnum, TypeVerificationAffranchissementEnum, TypeRegleMetierEnum
from models.business import RegleMetier, CodeAffranchissementFrauduleux
from tests.mocks.ssu_api import patch_ssu_tracking

class TestValidateurAffranchissementType:

    @pytest.fixture(autouse=True)
    def regle_fixture(self, db):

        # Delete all existing RegleMetier
        regles_to_delete = db.exec(select(RegleMetier).where(RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10, RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE)).all()
        for regle in regles_to_delete:
            db.delete(regle)
        db.commit()
        

        db.add(RegleMetier(cle="S10_LE", 
                            active=True,
                            type_affranchissement=TypeAffranchissementEnum.S10,
                            type_regle=TypeRegleMetierEnum.SEQUENCE, 
                            valeur={"debut": "01469910", "fin": "20469919", 
                                    "service": "LE", 
                                    "date_attribution": "01/10/2025"}))
        db.commit()
        
    @patch_ssu_tracking(finalized=False)
    def test_valid_sd87(self, mock):
        aff = ServiceAffranchissement.create_affranchissement({
            "code": "%000000087000915076713377276A10^1078b1c",
        })
        ValidateurAffranchissement.executer_validations(aff)
        assert aff.type == TypeAffranchissementEnum.SD
        assert aff.statut == StatutVerificationEnum.VALIDE

    @patch_ssu_tracking(finalized=False)
    def test_valid_s10(self, mock, db):
        from services.affranchissement import ServiceAffranchissement
        from services.validateurs.validateur_affranchissement import ValidateurAffranchissement
        
        codes = ["LE154792739FR", "LE144147991FR"] #"LE154450323FR"
        for code in codes:
            aff = ServiceAffranchissement.create_affranchissement({
                "code": code,
            })
            ValidateurAffranchissement.executer_validations(aff)
            assert aff.type == TypeAffranchissementEnum.S10
            assert aff.statut == StatutVerificationEnum.VALIDE, f"Statut invalide pour le code {code}"
            assert aff.verifications[-1].type == TypeVerificationAffranchissementEnum.SSU_TRACKING
            assert aff.verifications[-1].statut == StatutVerificationEnum.VALIDE

    @patch_ssu_tracking(finalized=True)
    def test_invalid_s10(self, mock, db):
        from services.affranchissement import ServiceAffranchissement
        from services.validateurs.validateur_affranchissement import ValidateurAffranchissement
        
        codes = ["LE154792739FR", "LE144147991FR"] #"LE154450323FR"
        for code in codes:
            aff = ServiceAffranchissement.create_affranchissement({
                "code": code,
            })
            ValidateurAffranchissement.executer_validations(aff)
            assert aff.type == TypeAffranchissementEnum.S10
            assert aff.statut == StatutVerificationEnum.INVALIDE, f"Statut invalide pour le code {code}"
            assert aff.verifications[-1].type == TypeVerificationAffranchissementEnum.SSU_TRACKING
            assert aff.verifications[-1].statut == StatutVerificationEnum.INVALIDE
    
    def test_presence_numero(self, db):
        code = "LE154792739FR"
        from models import CodeAffranchissementFrauduleux
        db.add(CodeAffranchissementFrauduleux(code=code))
        db.commit()
        
        aff = ServiceAffranchissement.create_affranchissement({
            "code": code,
        })
        ValidateurAffranchissement.executer_validations(aff)

        assert db.exec(select(CodeAffranchissementFrauduleux).where(CodeAffranchissementFrauduleux.code == code)).first() is not None

        assert aff.statut == StatutVerificationEnum.INVALIDE
        assert aff.verifications[-1].type == TypeVerificationAffranchissementEnum.CODE_FRAUDULEUX
        assert aff.verifications[-1].statut == StatutVerificationEnum.INVALIDE

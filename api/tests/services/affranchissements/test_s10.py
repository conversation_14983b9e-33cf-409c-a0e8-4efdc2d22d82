from services.affranchissement import ServiceAffranchissement
from constants.enumerations import TypeRegleMetierEnum, TypeAffranchissementEnum, CategorieAffranchissementEnum, DeviseEnum, SousTypeAffranchissementEnum
from models.business import Affranchissement, RegleMetier
from services.affranchissements.s10 import ServiceAffranchissementS10
from models.business import Affranchissement
from sqlmodel import select

class TestServiceAffranchissementS10:

    def test_sequence_associee(self, db):
        """
        Test sur la validité de la récupération d'une séquence associée à un S10
        
        """
       

        # Delete all existing RegleMetier
        regles_to_delete = db.exec(select(RegleMetier).where(
            RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
            RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE
        )).all()
        for regle in regles_to_delete:
            db.delete(regle)
        db.commit()
        
        # Créer des règles métier pour les tests
        db.add(RegleMetier(cle="S10_LL_0", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469910", "fin": "01469919", "service": "LL"},
                           active=True))
        
        db.add(RegleMetier(cle="S10_GENERIC", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01000000", "fin": "01999999", "service": None},
                           active=True))
        
        db.add(RegleMetier(cle="S10_LL_1", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469919", "fin": "01469929", "service": "LL"},
                           active=True))
        
        db.commit()
        
        # Test avec un code S10 correspondant à la règle spécifique
        affranchissement = Affranchissement(code="LL014699171FR", 
                                            categorie=CategorieAffranchissementEnum.CODE,
                                            type=TypeAffranchissementEnum.S10)

        service = ServiceAffranchissementS10(affranchissement)

        sequence = service.sequence_associee()
        assert sequence is not None
        assert sequence.cle == "S10_LL_0"
        
        # Test avec un code S10 hors plage
        affranchissement = Affranchissement(code="LL014699091FR", categorie=CategorieAffranchissementEnum.CODE, type=TypeAffranchissementEnum.S10)
        service = ServiceAffranchissementS10(affranchissement)
        
        sequence = service.sequence_associee()
        assert sequence is None
        
        # Test avec un service différent mais dans une plage inactive
        affranchissement = Affranchissement(code="LL014699271FR", categorie=CategorieAffranchissementEnum.CODE, type=TypeAffranchissementEnum.S10)
        service = ServiceAffranchissementS10(affranchissement)
        
        sequence = service.sequence_associee()
        assert sequence is not None
        assert sequence.cle == "S10_LL_1"
        
        # Test avec une règle inactive
        regle_to_update = db.exec(select(RegleMetier).where(RegleMetier.cle == "S10_LL_1")).first()
        if regle_to_update:
            regle_to_update.active = False
            db.add(regle_to_update)
        db.commit()
        sequence = service.sequence_associee()
        assert sequence.cle == "S10_LL_1"

    def test_valoriser_avec_valeur_zero(self, db):
        """
        Test de valorisation avec une séquence active ayant une valeur explicitement à 0
        """
        # Suppression des séquences existantes
        regles_to_delete = db.exec(select(RegleMetier).where(
            RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
            RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE
        )).all()
        for regle in regles_to_delete:
            db.delete(regle)
        db.commit()
        
        # Création d'une séquence avec valeur=0
        db.add(RegleMetier(cle="S10_LL_ZERO", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469917", "fin": "01469918", 
                                   "service": "LL", 
                                   "valeur": "0"},
                           active=True))
        db.commit()
        
        # Création de l'affranchissement
        affranchissement = Affranchissement(code="LL014699171FR", 
                                            categorie=CategorieAffranchissementEnum.CODE,
                                            type=TypeAffranchissementEnum.S10)
        
        # Valorisation
        service = ServiceAffranchissementS10(affranchissement)
        service.valoriser()
        
        # Vérification
        assert affranchissement.prix_unite_devise == 0
        assert affranchissement.devise == DeviseEnum.EURO

    def test_valoriser_avec_valeur_definie(self, db):
        """
        Test de valorisation avec une séquence active ayant une valeur définie à 5€
        """
        # Suppression des séquences existantes
        regles_to_delete = db.exec(select(RegleMetier).where(
            RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
            RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE
        )).all()
        for regle in regles_to_delete:
            db.delete(regle)
        db.commit()
        
        # Création d'une séquence avec valeur=5
        db.add(RegleMetier(cle="S10_LL_CINQ", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469917", "fin": "01469918", 
                                   "service": "LL", 
                                   "valeur": "5"},
                           active=True))
        db.commit()
        
        # Création de l'affranchissement
        affranchissement = Affranchissement(code="LL014699171FR", 
                                            categorie=CategorieAffranchissementEnum.CODE,
                                            type=TypeAffranchissementEnum.S10)
        
        # Valorisation
        service = ServiceAffranchissementS10(affranchissement)
        service.valoriser()
        
        # Vérification
        assert affranchissement.prix_unite_devise == 5
        assert affranchissement.devise == DeviseEnum.EURO

    def test_valoriser_sans_valeur_definie_philaposte_LE(self, db):
        """
        Test de valorisation avec une séquence active sans valeur définie, 
        mais avec emetteur=philaposte et service=LE (doit être 2.80€)
        """
        # Suppression des séquences existantes
        regles_to_delete = db.exec(select(RegleMetier).where(
            RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
            RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE
        )).all()
        for regle in regles_to_delete:
            db.delete(regle)
        db.commit()
        
        # Création d'une séquence sans valeur mais avec emetteur=philaposte
        db.add(RegleMetier(cle="S10_LE_PHILAPOSTE", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469917", "fin": "01469918", 
                                   "service": "LE", 
                                   "emetteur": "philaposte"},
                           active=True))
        db.commit()
        
        # Création de l'affranchissement
        affranchissement = Affranchissement(code="LE014699171FR", 
                                            categorie=CategorieAffranchissementEnum.CODE,
                                            type=TypeAffranchissementEnum.S10)
        
        # Valorisation
        service = ServiceAffranchissementS10(affranchissement)
        service.valoriser()
        
        # Vérification
        assert affranchissement.prix_unite_devise == 2.80
        assert affranchissement.devise == DeviseEnum.EURO

    def test_valoriser_sans_valeur_definie_non_philaposte(self, db):
        """
        Test de valorisation avec une séquence active sans valeur définie,
        et avec un émetteur autre que philaposte (doit être 0€)
        """
        # Suppression des séquences existantes
        regles_to_delete = db.exec(select(RegleMetier).where(
            RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
            RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE
        )).all()
        for regle in regles_to_delete:
            db.delete(regle)
        db.commit()
        
        # Création d'une séquence sans valeur et avec un autre émetteur
        db.add(RegleMetier(cle="S10_LL_AUTRE", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469917", "fin": "01469918", 
                                   "service": "LL", 
                                   "emetteur": "autre_emetteur"},
                           active=True))
        db.commit()
        
        # Création de l'affranchissement
        affranchissement = Affranchissement(code="LL014699171FR", 
                                            categorie=CategorieAffranchissementEnum.CODE,
                                            type=TypeAffranchissementEnum.S10)
        
        # Valorisation
        service = ServiceAffranchissementS10(affranchissement)
        service.valoriser()
        
        # Vérification
        assert affranchissement.prix_unite_devise == 0
        assert affranchissement.devise == DeviseEnum.EURO

    def test_valoriser_sequence_inactive(self, db):
        """
        Test de valorisation avec une séquence inactive (doit être 0€)
        """
        # Suppression des séquences existantes
        regles_to_delete = db.exec(select(RegleMetier).where(
            RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
            RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE
        )).all()
        for regle in regles_to_delete:
            db.delete(regle)
        db.commit()
        
        # Création d'une séquence inactive avec valeur=5
        db.add(RegleMetier(cle="S10_LL_INACTIVE", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469917", "fin": "01469918", 
                                   "service": "LL", 
                                   "valeur": "5"},
                           active=False))
        db.commit()
        
        # Création de l'affranchissement
        affranchissement = Affranchissement(code="LL014699171FR", 
                                            categorie=CategorieAffranchissementEnum.CODE,
                                            type=TypeAffranchissementEnum.S10)
        
        # Valorisation
        service = ServiceAffranchissementS10(affranchissement)
        service.valoriser()
        
        # Vérification
        assert affranchissement.prix_unite_devise == 0
        assert affranchissement.devise == DeviseEnum.EURO

    def test_valoriser_sans_sequence(self, db):
        """
        Test de valorisation sans séquence correspondante (doit être 0€)
        """
        # Suppression des séquences existantes
        regles_to_delete = db.exec(select(RegleMetier).where(
            RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
            RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE
        )).all()
        for regle in regles_to_delete:
            db.delete(regle)
        db.commit()
        
        # Création de l'affranchissement
        affranchissement = Affranchissement(code="LL014699171FR", 
                                            categorie=CategorieAffranchissementEnum.CODE,
                                            type=TypeAffranchissementEnum.S10)
        
        # Valorisation
        service = ServiceAffranchissementS10(affranchissement)
        service.valoriser()
        
        # Vérification
        assert affranchissement.prix_unite_devise == 0
        assert affranchissement.devise == DeviseEnum.EURO

    def test_valoriser_sequence_avec_valeur_non_numerique(self, db):
        """
        Test de valorisation avec une séquence inactive (doit être 0€)
        """
        # Suppression des séquences existantes
        regles_to_delete = db.exec(select(RegleMetier).where(
            RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
            RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE
        )).all()
        for regle in regles_to_delete:
            db.delete(regle)
        db.commit()
        
        # Création d'une séquence inactive avec valeur=5
        db.add(RegleMetier(cle="S10_LL_ACTIVE", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469917", "fin": "01469918", 
                                   "service": "LL", 
                                   "valeur": "5cinqua"},
                           active=True))
        db.commit()
        
        # Création de l'affranchissement
        affranchissement = Affranchissement(code="LL014699171FR", 
                                            categorie=CategorieAffranchissementEnum.CODE,
                                            type=TypeAffranchissementEnum.S10)
        
        # Valorisation
        service = ServiceAffranchissementS10(affranchissement)
        service.valoriser()
        
        # Vérification
        assert affranchissement.prix_unite_devise == 0
        assert affranchissement.devise == DeviseEnum.EURO

    def test_valoriser_avec_valeur_non_num_definie_philaposte_LE(self, db):
        """
        Test de valorisation avec une séquence active sans valeur définie, 
        mais avec emetteur=philaposte et service=LE (doit être 2.80€)
        """
        # Suppression des séquences existantes
        regles_to_delete = db.exec(select(RegleMetier).where(
            RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
            RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE
        )).all()
        for regle in regles_to_delete:
            db.delete(regle)
        db.commit()
        
        # Création d'une séquence sans valeur mais avec emetteur=philaposte
        db.add(RegleMetier(cle="S10_LE_PHILAPOSTE", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469917", "fin": "01469918", 
                                   "service": "LE", 
                                   "valeur": "5cinqua",
                                   "emetteur": "philaposte"},
                           active=True))
        db.commit()
        
        # Création de l'affranchissement
        affranchissement = Affranchissement(code="LE014699171FR", 
                                            categorie=CategorieAffranchissementEnum.CODE,
                                            type=TypeAffranchissementEnum.S10)
        
        # Valorisation
        service = ServiceAffranchissementS10(affranchissement)
        service.valoriser()
        
        # Vérification
        assert affranchissement.prix_unite_devise == 2.80
        assert affranchissement.devise == DeviseEnum.EURO

    def test_valoriser_avec_valeur_num_definie_philaposte_LE(self, db):
        """
        Test de valorisation avec une séquence active sans valeur définie, 
        mais avec emetteur=philaposte et service=LE (doit être 2.80€)
        """
        # Suppression des séquences existantes
        regles_to_delete = db.exec(select(RegleMetier).where(RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                                    RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE)).all()
        for regle in regles_to_delete:
            db.delete(regle)
        db.commit()
        
        # Création d'une séquence sans valeur mais avec emetteur=philaposte
        db.add(RegleMetier(cle="S10_LE_PHILAPOSTE", 
                           type_affranchissement=TypeAffranchissementEnum.S10,
                           type_regle=TypeRegleMetierEnum.SEQUENCE, 
                           valeur={"debut": "01469917", "fin": "01469918", 
                                   "service": "LE", 
                                   "valeur": "50",
                                   "emetteur": "philaposte"},
                           active=True))
        db.commit()
        
        # Création de l'affranchissement
        affranchissement = Affranchissement(code="LE014699171FR", 
                                            categorie=CategorieAffranchissementEnum.CODE,
                                            type=TypeAffranchissementEnum.S10)
        
        # Valorisation
        service = ServiceAffranchissementS10(affranchissement)
        service.valoriser()
        
        # Vérification
        assert affranchissement.prix_unite_devise == 50
        assert affranchissement.devise == DeviseEnum.EURO
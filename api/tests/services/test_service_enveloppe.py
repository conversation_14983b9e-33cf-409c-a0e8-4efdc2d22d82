import pytest
from datetime import datetime
from sqlmodel import Session, select
from models.business import Enveloppe, Affranchissement, Site, User
from services.enveloppe import ServiceEnveloppe
from constants.enumerations import DestinationEnveloppeEnum, TypeAffranchissementEnum, CategorieAffranchissementEnum, DeviseEnum, StatutEnveloppeEnum
from constants.variables import CONSTANTES_PRIX_AFFRANCHISSEMENT
from tests.fixtures.users import create_user
from tests.fixtures.sites import create_site


class TestServiceEnveloppe:
            
    def test_calcul_frais_postaux_dus_s10_international(self, db, user):
        """
        Test du calcul des frais postaux dus pour une enveloppe internationale avec S10
        """
        # Créer une enveloppe internationale
        enveloppe = Enveloppe(
            destination_enveloppe=DestinationEnveloppeEnum.INTERNATIONAL,
            poids=50,  # poids léger pour simplifier le test
            user_id=user.id,
            site_id=user.site_id
        )
        db.add(enveloppe)
        db.commit()
        
        # Récupérer le prix de base pour une lettre internationale
        service_enveloppe = ServiceEnveloppe(enveloppe)
        prix_base = service_enveloppe.calcul_frais_postaux_dus()
        
        # 1. Test avec un S10 qui commence par L
        affranchissement_l = Affranchissement(
            code="LA123456789FR",
            categorie=CategorieAffranchissementEnum.CODE,
            type=TypeAffranchissementEnum.S10,
            devise=DeviseEnum.EURO,
            enveloppe_id=enveloppe.id,
            donnees={"content": {"service": "L"}},
            user=user
        )
        db.add(affranchissement_l)
        db.commit()

        
        # 1. Vérifier que le prix inclut le supplément pour S10 international commençant par L
        prix_avec_s10_l = service_enveloppe.calcul_frais_postaux_dus()
        assert prix_avec_s10_l == prix_base + CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10
        
        # Supprimer les affranchissement associé
        affranchissements_to_delete = db.exec(select(Affranchissement).where(Affranchissement.enveloppe_id == enveloppe.id)).all()
        for affranchissement in affranchissements_to_delete:
            db.delete(affranchissement)
        db.commit()
        
        
        # 2. Test avec un S10 qui ne commence pas par L
        affranchissement_autre = Affranchissement(
            code="RA123456789FR",
            categorie=CategorieAffranchissementEnum.CODE,
            type=TypeAffranchissementEnum.S10,
            devise=DeviseEnum.EURO,
            enveloppe_id=enveloppe.id,
            donnees={"content": {"service": "R"}},
            user=user
        )
        db.add(affranchissement_autre)
        db.commit()
        
        # Vérifier que le prix n'inclut pas le supplément pour S10 international ne commençant pas par L
        prix_avec_s10_autre = service_enveloppe.calcul_frais_postaux_dus()
        assert prix_avec_s10_autre == prix_base
        
        # 3. Test avec à la fois un S10 L et un S10 non-L
        db.add(Affranchissement(
            code="LA123456789FR",
            categorie=CategorieAffranchissementEnum.CODE,
            type=TypeAffranchissementEnum.S10,
            devise=DeviseEnum.EURO,
            enveloppe_id=enveloppe.id,
            donnees={"content": {"service": "L"}},
            user=user
        ))
        db.commit()
        
        # Vérifier que le prix inclut le supplément (car présence d'un S10 L)
        prix_avec_deux_s10 = service_enveloppe.calcul_frais_postaux_dus()
        assert prix_avec_deux_s10 == prix_base + CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10
    
    def test_complet(self, db, user):
        """
        Test de la méthode complet() qui vérifie si une enveloppe est complète
        """
        # Créer une enveloppe
        enveloppe = Enveloppe(
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            user_id=user.id,
            site_id=user.site_id
        )
        db.add(enveloppe)
        db.commit()
        
        service_enveloppe = ServiceEnveloppe(enveloppe)
        
        # Sans photos, l'enveloppe n'est pas complète
        assert not service_enveloppe.complet()
        
        # Ajouter une photo
        from models.business import PhotoEnveloppe
        photo = PhotoEnveloppe(
            format="image/jpeg",
            enveloppe_id=enveloppe.id,
            url="https://example.com/photo.jpg"
        )
        enveloppe.photos.append(photo)
        db.commit()
        
        # Avec une photo et sans affranchissements non complets, l'enveloppe est complète
        assert service_enveloppe.complet()
    
    def test_valorisation(self, db, user):
        """
        Test de la méthode valoriser() qui calcule la valorisation d'une enveloppe
        """
        # Créer une enveloppe
        enveloppe = Enveloppe(
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            poids=50,
            user_id=user.id,
            site_id=user.site_id,
            statut=StatutEnveloppeEnum.TERMINEE
        )
        db.add(enveloppe)
        db.commit()
        
        service_enveloppe = ServiceEnveloppe(enveloppe)
        service_enveloppe.valoriser()
        
        # Vérifier que la valorisation a été calculée
        assert enveloppe.valorisation is not None
        assert "postage" in enveloppe.valorisation
        assert "livraison" in enveloppe.valorisation
        assert "collecte" in enveloppe.valorisation
        assert "expédition" in enveloppe.valorisation

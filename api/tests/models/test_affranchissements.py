import pytest
from sqlmodel import Session, select, Boolean
from datetime import datetime
from models.business import Enveloppe, Site, User, Expediteur, Destination
from constants.enumerations import StatutEnveloppeEnum, ProduitEnum
from uuid import UUID
from models.public import  CategorieAffranchissementEnum, TypeAffranchissementEnum, DeviseEnum
from core.utils.prix import convertir_prix_devise_en_euros
from models.public import ModeleAffranchissement
from models.business import Affranchissement

class TestAffranchissementModel:
       
    def test_prix_unite_euros(self, enveloppe, db, user):
        # Test pour l'euro
        affranchissement_euro = Affranchissement(
            categorie=CategorieAffranchissementEnum.MARI,
            type=TypeAffranchissementEnum.VAL,
            devise=DeviseEnum.EURO,
            prix_unite_devise=1.26,
            enveloppe_id=enveloppe.id,
            user_id=user.id
        )
        db.add(affranchissement_euro)
        db.commit()
        assert affranchissement_euro.prix_unite_euros == 1.26

        # Test pour les francs
        affranchissement_francs = Affranchissement(
            categorie=CategorieAffranchissementEnum.MARI,
            type=TypeAffranchissementEnum.VAL,
            devise=DeviseEnum.FRANCS,
            prix_unite_devise=10,
            enveloppe_id=enveloppe.id,
            user_id=user.id
        )
        db.add(affranchissement_francs)
        db.commit()
        assert round(affranchissement_francs.prix_unite_euros, 2) == round(convertir_prix_devise_en_euros(DeviseEnum.FRANCS, 10), 2)

        # Test pour les anciens francs
        affranchissement_anciens_francs = Affranchissement(
            categorie=CategorieAffranchissementEnum.MARI,
            type=TypeAffranchissementEnum.VAL,
            devise=DeviseEnum.ANCIEN_FRANCS,
            prix_unite_devise=1000,
            enveloppe_id=enveloppe.id,
            user_id=user.id
        )
        db.add(affranchissement_anciens_francs)
        db.commit()
        assert round(affranchissement_anciens_francs.prix_unite_euros, 2) == round(convertir_prix_devise_en_euros(DeviseEnum.ANCIEN_FRANCS, 1000), 2)

    def test_modele_affranchissement(self, monkeypatch):
        # Ajout d'un compteur pour le constructeur
        original_init = ModeleAffranchissement.__init__
        call_count = 0
        
        def counting_init(self, *args, **kwargs):
            nonlocal call_count
            call_count += 1
            original_init(self, *args, **kwargs)
        
        monkeypatch.setattr(ModeleAffranchissement, "__init__", counting_init)
        
        # Cas1: Affranchissement VALORISABLE car ancien par exemple
        modele = ModeleAffranchissement(
            categorie=CategorieAffranchissementEnum.MARI,
            type=TypeAffranchissementEnum.VAL,
            color="xtra_envelop_element_code",
            variant="outlined",
            devise=DeviseEnum.EURO
        )

        assert modele.valorisable is True
        assert modele.informations["champs_requis"] == ["prix_unite_devise"]

        # Cas 2: Affranchissement AUTO valorisé par Service et règle métier
        modele = ModeleAffranchissement(
            categorie=CategorieAffranchissementEnum.CODE,
            type=TypeAffranchissementEnum.S10,
            color="xtra_envelop_element_code",
            variant="outlined",
            valorisation_complexe=True
        )

        assert modele.valorisable is False
        assert modele.valorisation_complexe is True
        assert modele.produit_postal is None
        # assert modele.informations["champs_requis"] == ["prix_unite_devise"]
        return
        # Cas 3: Affranchissement AUTO valorisé par code_produit LAPOSTE
        modele = ModeleAffranchissement(
            categorie=CategorieAffranchissementEnum.MARI,
            type=TypeAffranchissementEnum.DEWD1,
            color="xtra_envelop_element_code",
            variant="outlined",
            code_produit="1123820"
        )
        assert modele.valorisable is False
        assert modele.valorisation_complexe is False
        assert modele.produit_postal is not None
        assert modele.devise == DeviseEnum.EURO
        assert modele.prix_unite_devise == 1.39
        
        # Vérification du nombre d'appels au constructeur de ModeleAffranchissement
        assert call_count == 3


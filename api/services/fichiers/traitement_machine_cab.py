# Permet de lire et traiter un fichier de machine CAB

import csv
from sqlmodel import select
from core.db import get_session
from models.business import Enveloppe, Site, User
from constants.enumerations import ProduitEnum, StatutVerificationEnum
from services.affranchissement import ServiceAffranchissement

def lire_csv(chemin_fichier, session):
    data = {}

    site = session.exec(select(Site).where(Site.nom == "ROISSY")).first()
    user = session.exec(select(User)).first()

    listes_affranchissements = []

    with open(chemin_fichier, mode='r', newline='', encoding='utf-8') as fichier:
        lignes = list(csv.DictReader(fichier))

        # data["sans_code"] = len(list(filter(lambda x: x.get('TackingBarcode1') == 'ANNOUNCEMENT_POSTAL_CODE', lignes)))
        # data["avec_code"] = len(list(filter(lambda x: x.get('TackingBarcode1') != 'ANNOUNCEMENT_POSTAL_CODE', lignes)))
        
        # data["poids_certifie"] = len(list(filter(lambda x: x.get('Weight_Certified') == 'True', lignes)))
        # data["poids_non_certifie"] = len(list(filter(lambda x: x.get('Weight_Certified') != 'True', lignes)))
        
        # data["dimension_certifie"] = len(list(filter(lambda x: x.get('DIM_Cerified') == 'True', lignes)))
        # data["dimension_non_certifie"] = len(list(filter(lambda x: x.get('DIM_Cerified') != 'True', lignes)))

        for line in lignes:
            line['statut'] = 'INVALIDE'
            
            enveloppe = Enveloppe(
                destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
                poids=line.get('weight'),
                site_id=site.id,
                user_id=user.id,
            )

            code = line.get('TackingBarcode1')

            if code in ['ANNOUNCEMENT_POSTAL_CODE', 'ANNOUNCEMENT_POSTAL_CODE_2', 'MANUAL_POSTAL_CODE']:
                continue

            try:
                aff = ServiceAffranchissement.create_affranchissement({
                    "code": code
                })

                aff.enveloppe_id = enveloppe.id
                session.add(aff)
                session.add(enveloppe)
                listes_affranchissements.append(aff)

                line['statut'] = str(aff.statut.value)

            except Exception as e:
                pass

        # session.commit()    

        print("Valides: ", len(list(filter(lambda x: x.statut == StatutVerificationEnum.VALIDE, listes_affranchissements))))
        print("Invalides: ", len(list(filter(lambda x: x.statut == StatutVerificationEnum.INVALIDE, listes_affranchissements))))


        #  Écriture dans un nouveau fichier CSV avec la nouvelle colonne
        with open('/data/DT - Liste CAB Par Code Pays DIM v1(4)_statut.csv', mode='w', newline='', encoding='utf-8') as nouveau_fichier:
            fieldnames = lignes[0].keys()
            writer = csv.DictWriter(nouveau_fichier, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(lignes)


if __name__ == "__main__":
    # Exemple d'utilisation
    chemin_fichier = '/data/DT - Liste CAB Par Code Pays DIM v1(4).csv'

    with get_session() as session:
        lire_csv(chemin_fichier, session)



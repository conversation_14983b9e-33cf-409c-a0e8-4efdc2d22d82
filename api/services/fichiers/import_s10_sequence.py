#!/usr/bin/env python3
"""
Script pour importer un fichier Excel S10 et créer des règles métier.
"""
import sys
import argparse
from pathlib import Path
from sqlalchemy import JSO<PERSON>, cast, func, text
from sqlmodel import select
from core.db import get_session
from services.fichiers.import_s10_sequences import import_sequence_s10_depuis_excel
from models.business import RegleMetier
from constants.enumerations import TypeRegleMetierEnum, TypeAffranchissementEnum

def regle_existe(regle):
    with get_session() as session:
        existing_rules = session.exec(select(RegleMetier).where(
                            RegleMetier.type_affranchissement == regle.type_affranchissement,
                            RegleMetier.type_regle == regle.type_regle,
                            func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == regle.valeur.get('service'),
                            func.json_extract_path_text(cast(RegleMetier.valeur, JSO<PERSON>), 'debut') == regle.valeur.get('debut'),
                            func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') == regle.valeur.get('fin'),
                            func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'date_attribution') == regle.valeur.get('date_attribution')
                        )).first()

        return existing_rules

def get_last_id(service):
    """Récupère le dernier ID utilisé pour les règles S10_LW"""
    with get_session() as session:
        last_rule = session.execute(
            text(f"""
            SELECT * FROM public.reglemetier WHERE cle LIKE '%S10_{service}_%'
            ORDER BY id DESC LIMIT 1
            """)
        ).fetchone()
        
        if last_rule:
            # Extraire l'ID à partir de la clé (dernier élément après le dernier '_')
            try:
                return int(last_rule.cle.split('_')[-1]) + 1
            except (ValueError, IndexError):
                return 0
        return 0

def main():
    """Point d'entrée principal du script."""
    parser = argparse.ArgumentParser(description="Importe un fichier Excel S10 et crée des règles métier")
    parser.add_argument("file_path", help="Chemin vers le fichier Excel à importer")
    parser.add_argument("service", help="service")
    parser.add_argument("--dry-run", action="store_true", help="Exécuter sans enregistrer en base de données")
    args = parser.parse_args()

    file_path = args.file_path
    dry_run = args.dry_run
    service = args.service.upper()
    id = get_last_id(service)

    print(f"Commence a l'ID : {id}")

    # Vérifier si le fichier existe
    if not Path(file_path).exists():
        print(f"Erreur: Le fichier {file_path} n'existe pas")
        sys.exit(1)

    try:
        # Importer les règles depuis le fichier Excel
        print(f"Importation du fichier {file_path}...")
        rules = import_sequence_s10_depuis_excel(file_path)

        print(f"{len(rules)} règles métier créées")

        # Filtrer les règles qui existent déjà
        rules_filtrees = []
        for rule in rules:

            if rule.valeur.get("service") != service:
                continue

            if not regle_existe(rule):
                rule.cle = "_".join(rule.cle.split("_")[:-1]) + f"_{id}"
                id += 1
                rules_filtrees.append(rule)
                
        
        rules = rules_filtrees
        print(f"{len(rules)} règles après filtrage des doublons")

        if dry_run:
            print("Mode dry-run: les règles ne seront pas enregistrées en base de données")
            for rule in rules:
                print(f"- {rule.cle}: {rule.valeur}")
        else:
            # Enregistrer les règles en base de données
            with get_session() as session:
                print("Enregistrement des règles en base de données...")
                session.add_all(rules)
                session.commit()
                print("Règles enregistrées avec succès")

    except Exception as e:
        print(f"Erreur lors de l'importation: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
